#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""大规模LLM API调用的并发处理引擎

主要功能:
- 多线程并发处理大规模API请求
- 自动处理限流、重试和错误恢复
- 支持实时监控和状态追踪
- 异步结果写入和文件管理

使用示例:
    def construct_prompt(data):
        return "指令", f"处理数据: {data['content']}"

    def process_response(task, response):
        return {"id": task["id"], "result": response}

    config = {
        "API_KEY": "your-api-key",
        "API_URL": "https://api.example.com/v1/chat",
        "MODEL": "gpt-3.5-turbo"
    }

    run_synthesis(
        input_dir="data/input",
        output_dir="data/output",
        construct_prompt_func=construct_prompt,
        process_response_func=process_response,
        config=config
    )

注意事项:
- 输入文件必须是.jsonl格式
- 需要正确配置API密钥和URL
- 建议根据API限制调整并发数和重试参数
- 支持优雅中断(Ctrl+C)，会保证正在处理的任务完成
"""
import json
import os
import time
import threading
from queue import Queue
from typing import Dict, Any, Callable
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import logging
from datetime import datetime

class SynthesisEngine:
    def __init__(self,
                 config: Dict[str, Any],
                 construct_prompt_func: Callable,
                 process_response_func: Callable):
        """初始化合成引擎

        Args:
            config: 配置参数字典
            construct_prompt_func: 构造提示词的函数
            process_response_func: 处理响应的函数
        """
        # 存储业务回调函数
        self.construct_prompt = construct_prompt_func
        self.process_response = process_response_func

        # 配置项提取
        self.INPUT_DIR = config.get("INPUT_DIR", "data/input")
        self.OUTPUT_DIR = config.get("OUTPUT_DIR", "data/output")
        self.API_URL = config.get("API_URL", "")
        self.API_KEY = config.get("API_KEY", "")
        self.MAX_RETRIES = config.get("MAX_RETRIES", 3)
        self.NUM_PROCESSORS = config.get("NUM_PROCESSORS", 4)
        self.RATE_LIMIT_WAIT = config.get("RATE_LIMIT_WAIT", 10)
        self.MODEL = config.get("MODEL", "")
        self.MAX_QUEUE_SIZE = config.get("MAX_QUEUE_SIZE", 500)  # 增大队列容量
        self.API_PARAMS = config.get("API_PARAMS", {})
        self.SYSTEM_PAUSE_TIMEOUT = config.get("SYSTEM_PAUSE_TIMEOUT", 300)  # 系统暂停超时时间

        # 数据结构 - 使用无限队列避免阻塞
        self.task_queue = Queue()  # 移除maxsize限制
        self.result_queue = Queue()
        self.system_state = threading.Event()
        self.system_state.set()  # 默认为运行状态
        self.system_shutdown = threading.Event()  # 系统关闭信号
        self.all_producers_done = False
        self.active_processors = 0  # 活跃的处理器数量
        self.pending_retries = 0  # 等待重试的任务数量
        self.total_tasks_added = 0  # 总添加任务数
        self.pause_timer = None  # 暂停恢复定时器

        # 统计数据
        self.stats_lock = threading.Lock()
        self.stats = {
            "processed": 0,
            "success": 0,
            "failed": 0,
            "retries": 0,
            "last_error": None,
            "total_tasks": 0  # 总任务数
        }

        # 设置日志
        self.logger = self.setup_logging()

        # 设置HTTP会话和连接池
        self.session = self.setup_http_session()

    def setup_logging(self):
        """配置日志"""
        os.makedirs("logs", exist_ok=True)
        log_file = f"logs/synthesis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

        # 创建专用的logger，避免影响全局logging配置
        logger = logging.getLogger(f"synthesis_engine_{id(self)}")
        logger.setLevel(logging.INFO)

        # 避免重复添加handler
        if not logger.handlers:
            # 文件handler
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(logging.INFO)

            # 控制台handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)

            # 设置格式
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)

            logger.addHandler(file_handler)
            logger.addHandler(console_handler)

        return logger

    def setup_http_session(self):
        """设置HTTP会话和连接池"""
        session = requests.Session()

        # 配置重试策略 - 只重试网络层错误，不重试业务逻辑错误
        retry_strategy = Retry(
            total=2,  # 减少重试次数，避免与应用层重试重复
            backoff_factor=0.5,
            status_forcelist=[500, 502, 503, 504],
            allowed_methods=["POST"]
        )

        # 配置HTTP适配器
        adapter = HTTPAdapter(
            pool_connections=self.NUM_PROCESSORS,
            pool_maxsize=self.NUM_PROCESSORS * 2,
            max_retries=retry_strategy
        )

        session.mount("http://", adapter)
        session.mount("https://", adapter)

        return session

    def pause_system(self, reason, duration=None):
        """暂停系统运行一段时间，带超时保护"""
        if duration is None:
            duration = self.RATE_LIMIT_WAIT

        if self.system_state.is_set():  # 只有在系统运行时才需要暂停
            self.system_state.clear()  # 暂停系统
            self.logger.info(f"系统暂停: {reason}，将在{duration}秒后恢复")

            # 取消之前的定时器
            if self.pause_timer:
                self.pause_timer.cancel()

            # 使用定时器在指定时间后恢复系统
            def resume():
                if not self.system_state.is_set() and not self.system_shutdown.is_set():
                    self.system_state.set()
                    self.logger.info("系统已恢复运行")
                    self.pause_timer = None

            self.pause_timer = threading.Timer(duration, resume)
            self.pause_timer.start()

    def force_resume_system(self):
        """强制恢复系统运行"""
        if self.pause_timer:
            self.pause_timer.cancel()
            self.pause_timer = None
        if not self.system_state.is_set():
            self.system_state.set()
            self.logger.warning("系统被强制恢复运行")

    def shutdown_system(self):
        """关闭系统"""
        self.system_shutdown.set()
        if self.pause_timer:
            self.pause_timer.cancel()
            self.pause_timer = None
        self.system_state.set()  # 确保所有等待的线程能够退出

    class TaskProducer(threading.Thread):
        """从输入文件读取数据并添加到任务队列"""

        def __init__(self, engine):
            super().__init__()
            self.engine = engine

        def run(self):
            engine = self.engine
            engine.logger.info("任务生产者启动")
            task_count = 0
            try:
                for filename in os.listdir(engine.INPUT_DIR):
                    if not filename.endswith(".jsonl"):
                        continue

                    # 检查系统是否关闭
                    if engine.system_shutdown.is_set():
                        engine.logger.info("系统关闭信号，任务生产者退出")
                        break

                    input_path = os.path.join(engine.INPUT_DIR, filename)
                    output_path = os.path.join(engine.OUTPUT_DIR, filename)

                    # 创建输出目录
                    os.makedirs(os.path.dirname(output_path), exist_ok=True)

                    engine.logger.info(f"开始处理文件: {filename}")
                    file_task_count = 0

                    try:
                        with open(input_path, "r", encoding="utf-8") as f_in:
                            for line_num, line in enumerate(f_in, 1):
                                # 检查系统是否关闭
                                if engine.system_shutdown.is_set():
                                    engine.logger.info("系统关闭信号，停止添加任务")
                                    break

                                # 系统暂停时等待，但有超时保护
                                if not engine.system_state.is_set():
                                    engine.logger.info("系统暂停中，等待恢复...")
                                    if not engine.system_state.wait(timeout=engine.SYSTEM_PAUSE_TIMEOUT):
                                        engine.logger.warning(f"系统暂停超时({engine.SYSTEM_PAUSE_TIMEOUT}秒)，强制恢复")
                                        engine.force_resume_system()

                                try:
                                    data = json.loads(line.strip())
                                    if "id" not in data:
                                        engine.logger.warning(f"文件 {filename} 第 {line_num} 行缺少id字段，跳过")
                                        continue

                                    # 使用业务回调函数构造提示词，添加异常保护
                                    try:
                                        instruction, prompt = engine.construct_prompt(data)
                                    except Exception as e:
                                        engine.logger.error(f"构造提示词失败 {filename}:{line_num} - {str(e)}")
                                        continue

                                    # 创建任务
                                    task = {
                                        "id": data["id"],
                                        "instruction": instruction,
                                        "prompt": prompt,
                                        "retries": 0,
                                        "output_file": output_path,
                                        "raw_data": data,
                                        "source_file": filename,
                                        "line_number": line_num
                                    }

                                    # 添加到队列（无限队列，不会阻塞）
                                    engine.task_queue.put(task)
                                    task_count += 1
                                    file_task_count += 1

                                except json.JSONDecodeError as e:
                                    engine.logger.error(f"JSON解析错误 {filename}:{line_num} - {str(e)}")
                                    continue
                                except Exception as e:
                                    engine.logger.error(f"处理行数据出错 {filename}:{line_num} - {str(e)}")
                                    continue

                    except FileNotFoundError:
                        engine.logger.error(f"文件不存在: {input_path}")
                        continue
                    except Exception as e:
                        engine.logger.error(f"读取文件出错 {filename}: {str(e)}")
                        continue

                    engine.logger.info(f"文件 {filename} 处理完成，添加了 {file_task_count} 个任务")

                # 更新总任务数统计
                with engine.stats_lock:
                    engine.stats["total_tasks"] = task_count
                    engine.total_tasks_added = task_count

                engine.logger.info(f"所有任务已添加到队列，总计 {task_count} 个任务")
            except Exception as e:
                engine.logger.error(f"任务生产者出错: {str(e)}")
            finally:
                # 添加结束标记
                for _ in range(engine.NUM_PROCESSORS):
                    engine.task_queue.put(None)
                engine.logger.info("任务生产者已完成")

    class TaskProcessor(threading.Thread):
        """从任务队列获取任务并处理"""

        def __init__(self, engine, worker_id):
            super().__init__()
            self.engine = engine
            self.worker_id = worker_id

        def run(self):
            engine = self.engine
            engine.logger.info(f"处理者 #{self.worker_id} 启动")

            # 增加活跃处理器计数
            with engine.stats_lock:
                engine.active_processors += 1

            consecutive_timeouts = 0  # 连续超时计数
            max_consecutive_timeouts = 30  # 最大连续超时次数

            while True:
                try:
                    # 检查系统是否关闭
                    if engine.system_shutdown.is_set():
                        engine.logger.info(f"处理者 #{self.worker_id} 收到关闭信号")
                        break

                    # 系统暂停时等待，但有超时保护
                    if not engine.system_state.is_set():
                        engine.logger.info(f"处理者 #{self.worker_id} 等待系统恢复...")
                        if not engine.system_state.wait(timeout=engine.SYSTEM_PAUSE_TIMEOUT):
                            engine.logger.warning(f"处理者 #{self.worker_id} 等待系统恢复超时，强制恢复")
                            engine.force_resume_system()

                    # 获取任务 (timeout避免一直阻塞)
                    try:
                        task = engine.task_queue.get(timeout=2)
                        consecutive_timeouts = 0  # 重置超时计数
                    except:
                        consecutive_timeouts += 1
                        # 检查是否应该退出
                        if engine.all_producers_done and engine.task_queue.empty() and engine.pending_retries == 0:
                            engine.logger.info(f"处理者 #{self.worker_id} 检测到所有任务完成，准备退出")
                            break
                        # 如果连续超时太多次，可能系统有问题
                        if consecutive_timeouts >= max_consecutive_timeouts:
                            engine.logger.warning(f"处理者 #{self.worker_id} 连续超时 {consecutive_timeouts} 次，检查系统状态")
                            consecutive_timeouts = 0
                        continue

                    # 接收到结束信号
                    if task is None:
                        engine.logger.info(f"处理者 #{self.worker_id} 收到结束信号")
                        engine.task_queue.task_done()
                        break

                    # 处理任务
                    try:
                        result = self.process_task(task)

                        if result:
                            # 成功处理，添加到结果队列
                            engine.result_queue.put(result)

                            with engine.stats_lock:
                                engine.stats["success"] += 1
                                engine.stats["processed"] += 1

                    except Exception as e:
                        # 有任何错误就重试
                        self.handle_error(task, str(e))

                    # 标记任务完成
                    engine.task_queue.task_done()

                except Exception as e:
                    # 其他未预期的错误
                    engine.logger.error(f"处理者 #{self.worker_id} 运行出错: {str(e)}")
                    time.sleep(1)  # 避免CPU空转

            # 减少活跃处理器计数
            with engine.stats_lock:
                engine.active_processors -= 1
            engine.logger.info(f"处理者 #{self.worker_id} 已停止")

        def process_task(self, task):
            """处理单个任务，增强错误处理"""
            engine = self.engine

            try:
                # 构建API请求参数
                api_params = engine.API_PARAMS.copy()
                api_params.update({
                    "model": engine.MODEL,
                    "messages": [{"role": "user", "content": task["prompt"]}]
                })

                # 只有在配置中明确指定时才添加reasoning参数
                if "reasoning" in engine.API_PARAMS:
                    api_params["reasoning"] = engine.API_PARAMS["reasoning"]

                # 发送API请求
                response = engine.session.post(
                    engine.API_URL,
                    headers={
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {engine.API_KEY}"
                    },
                    json=api_params,
                    timeout=120
                )

                # 检查状态码
                if response.status_code != 200:
                    error_msg = f"API错误: {response.status_code}"

                    # 尝试获取错误详情
                    try:
                        error_detail = response.json()
                        if "error" in error_detail:
                            error_msg += f" - {error_detail['error']}"
                    except:
                        pass

                    # 处理限流错误
                    if response.status_code in [429, 432, 433]:
                        error_msg = f"限流错误: {response.status_code}"
                        engine.pause_system(error_msg, engine.RATE_LIMIT_WAIT)

                    # 处理认证错误
                    if response.status_code == 401:
                        engine.logger.critical("认证失败，程序终止")
                        engine.shutdown_system()
                        raise Exception("认证失败")

                    raise Exception(error_msg)

                # 获取API响应结果
                try:
                    result = response.json()
                except json.JSONDecodeError as e:
                    raise Exception(f"API响应JSON解析失败: {str(e)}")

                # 验证响应格式
                if "choices" not in result or not result["choices"]:
                    raise Exception("API响应格式错误：缺少choices字段")

                choice = result["choices"][0]
                if "message" not in choice or "content" not in choice["message"]:
                    raise Exception("API响应格式错误：缺少message.content字段")

                llm_response = choice["message"]["content"]

                # 获取reasoning字段（如果存在）
                reasoning = ""
                if "reasoning" in choice["message"]:
                    reasoning = choice["message"]["reasoning"]
                    engine.logger.debug(f"任务 {task['id']} 获取reasoning字段，长度: {len(reasoning)}")

                # 使用业务处理函数处理响应，添加异常保护
                try:
                    processed_result = engine.process_response(task, llm_response, reasoning)
                except Exception as e:
                    engine.logger.error(f"业务处理函数出错 任务{task['id']}: {str(e)}")
                    raise Exception(f"业务处理失败: {str(e)}")

                # 确保返回结果包含必要字段
                if not isinstance(processed_result, dict):
                    raise Exception("业务处理函数返回值必须是字典")

                processed_result["output_file"] = task["output_file"]
                processed_result["task_id"] = task["id"]

                return processed_result

            except Exception as e:
                # 记录详细的错误信息
                error_info = f"任务 {task['id']} 处理失败: {str(e)}"
                if "source_file" in task:
                    error_info += f" (来源: {task['source_file']}:{task.get('line_number', '?')})"
                engine.logger.error(error_info)
                raise

        def handle_error(self, task, error_msg):
            """处理任务错误，改进重试机制"""
            engine = self.engine

            # 记录错误详情
            error_detail = f"任务 {task['id']} 处理失败: {error_msg}"
            if "source_file" in task:
                error_detail += f" (来源: {task['source_file']}:{task.get('line_number', '?')})"
            engine.logger.error(error_detail)

            with engine.stats_lock:
                engine.stats["retries"] += 1
                engine.stats["last_error"] = error_msg

            # 检查是否应该重试
            if task["retries"] < engine.MAX_RETRIES:
                task["retries"] += 1
                backoff_time = min(5 * task["retries"], 30)  # 最大30秒回退

                engine.logger.info(f"任务 {task['id']} 将在 {backoff_time} 秒后重试 (第 {task['retries']} 次)")

                # 原子性地增加等待重试的任务计数
                with engine.stats_lock:
                    engine.pending_retries += 1

                # 延迟重新入队
                def requeue():
                    try:
                        # 检查系统是否已关闭
                        if engine.system_shutdown.is_set():
                            engine.logger.info(f"系统已关闭，取消重试任务 {task['id']}")
                            with engine.stats_lock:
                                engine.pending_retries -= 1
                                engine.stats["failed"] += 1
                                engine.stats["processed"] += 1
                            return

                        engine.task_queue.put(task)
                        engine.logger.info(f"任务 {task['id']} 已重新入队")

                        # 原子性地减少等待重试的任务计数
                        with engine.stats_lock:
                            engine.pending_retries -= 1

                    except Exception as e:
                        engine.logger.error(f"重新入队任务 {task['id']} 失败: {str(e)}")
                        # 确保计数一致性
                        with engine.stats_lock:
                            engine.pending_retries -= 1
                            engine.stats["failed"] += 1
                            engine.stats["processed"] += 1

                # 使用定时器延迟重试
                retry_timer = threading.Timer(backoff_time, requeue)
                retry_timer.daemon = True  # 设置为守护线程
                retry_timer.start()
            else:
                # 达到最大重试次数，标记为失败
                engine.logger.warning(f"任务 {task['id']} 已达最大重试次数({engine.MAX_RETRIES})，标记为失败")
                with engine.stats_lock:
                    engine.stats["failed"] += 1
                    engine.stats["processed"] += 1

    class ResultWriter(threading.Thread):
        """从结果队列获取结果并写入文件"""

        def __init__(self, engine):
            super().__init__()
            self.engine = engine

        def run(self):
            engine = self.engine
            engine.logger.info("结果写入者启动")

            # 用于跟踪已处理的文件
            processed_files = {}
            consecutive_timeouts = 0
            max_consecutive_timeouts = 60  # 最大连续超时次数

            while True:
                try:
                    # 检查系统是否关闭
                    if engine.system_shutdown.is_set():
                        engine.logger.info("结果写入者收到关闭信号")
                        break

                    # 获取结果 (使用timeout避免永久阻塞)
                    try:
                        result = engine.result_queue.get(timeout=2)
                        consecutive_timeouts = 0  # 重置超时计数
                    except:
                        consecutive_timeouts += 1
                        # 检查是否所有任务都已完成
                        if (engine.all_producers_done and
                            engine.task_queue.empty() and
                            engine.pending_retries == 0 and
                            engine.result_queue.empty()):
                            engine.logger.info("结果写入者检测到所有任务完成")
                            break
                        # 如果连续超时太多次，可能有问题
                        if consecutive_timeouts >= max_consecutive_timeouts:
                            engine.logger.warning(f"结果写入者连续超时 {consecutive_timeouts} 次")
                            consecutive_timeouts = 0
                        continue

                    # 接收到结束信号
                    if result is None:
                        engine.logger.info("结果写入者收到结束信号")
                        engine.result_queue.task_done()
                        break

                    # 写入结果
                    try:
                        output_file = result["output_file"]

                        # 确保输出目录存在
                        os.makedirs(os.path.dirname(output_file), exist_ok=True)

                        with open(output_file, "a", encoding="utf-8") as f_out:
                            # 删除输出文件路径再写入
                            result_copy = result.copy()
                            result_copy.pop("output_file", None)
                            f_out.write(json.dumps(result_copy, ensure_ascii=False) + "\n")

                        # 更新统计
                        if output_file not in processed_files:
                            processed_files[output_file] = 0
                        processed_files[output_file] += 1

                    except Exception as e:
                        engine.logger.error(f"写入结果失败: {str(e)}")
                        # 写入失败也要标记任务完成，避免阻塞

                    # 标记结果处理完成
                    engine.result_queue.task_done()

                except Exception as e:
                    engine.logger.error(f"结果写入者出错: {str(e)}")
                    time.sleep(1)  # 发生真正错误时暂停一秒

            engine.logger.info(f"结果写入者已完成，共处理 {sum(processed_files.values())} 个结果")

    class Monitor(threading.Thread):
        """监控系统状态"""

        def __init__(self, engine):
            super().__init__()
            self.engine = engine
            self.no_progress_count = 0  # 无进展计数器
            self.last_processed = 0  # 上次处理的任务数

        def run(self):
            engine = self.engine
            engine.logger.info("监控线程启动")

            while True:
                try:
                    # 检查系统是否关闭
                    if engine.system_shutdown.is_set():
                        engine.logger.info("监控线程收到关闭信号")
                        break

                    with engine.stats_lock:
                        # 计算进度百分比
                        progress_pct = 0
                        if engine.stats["total_tasks"] > 0:
                            progress_pct = (engine.stats["processed"] / engine.stats["total_tasks"]) * 100

                        status = (
                            f"状态：{'运行中' if engine.system_state.is_set() else '暂停中'} | "
                            f"进度：{engine.stats['processed']}/{engine.stats['total_tasks']} ({progress_pct:.1f}%) | "
                            f"任务队列：{engine.task_queue.qsize()} | "
                            f"结果队列：{engine.result_queue.qsize()} | "
                            f"活跃处理器：{engine.active_processors} | "
                            f"等待重试：{engine.pending_retries} | "
                            f"成功：{engine.stats['success']} | "
                            f"失败：{engine.stats['failed']} | "
                            f"重试：{engine.stats['retries']}"
                        )

                        if engine.stats["last_error"]:
                            status += f" | 最新错误：{engine.stats['last_error']}"
                            engine.stats["last_error"] = None  # 清除错误

                        # 检查是否所有任务都已完成的条件
                        all_tasks_done = (
                            engine.all_producers_done and  # 生产者已完成
                            engine.task_queue.empty() and  # 任务队列为空
                            engine.pending_retries == 0 and  # 没有等待重试的任务
                            engine.result_queue.empty()  # 结果队列为空
                        )

                        # 检查是否达到预期的任务总数
                        expected_completion = (
                            engine.all_producers_done and
                            engine.stats["total_tasks"] > 0 and
                            engine.stats["processed"] >= engine.stats["total_tasks"]
                        )

                    engine.logger.info(status)

                    # 检查是否所有任务都已完成
                    if all_tasks_done or expected_completion:
                        engine.logger.info("所有任务已处理完毕，准备退出")

                        # 向结果写入者发送结束信号
                        engine.result_queue.put(None)
                        break

                    # 检查是否有进展
                    current_processed = engine.stats['processed']
                    if current_processed == self.last_processed:
                        self.no_progress_count += 1
                        if self.no_progress_count >= 24:  # 120秒无进展
                            engine.logger.warning(f"系统120秒无进展，当前状态: 队列{engine.task_queue.qsize()}, 重试{engine.pending_retries}, 活跃处理器{engine.active_processors}")

                            # 如果生产者已完成且队列为空，强制退出
                            if engine.all_producers_done and engine.task_queue.empty():
                                engine.logger.warning("强制退出系统")
                                engine.shutdown_system()
                                engine.result_queue.put(None)
                                break

                            # 如果系统暂停太久，强制恢复
                            if not engine.system_state.is_set():
                                engine.logger.warning("系统暂停太久，强制恢复")
                                engine.force_resume_system()

                    else:
                        self.no_progress_count = 0
                        self.last_processed = current_processed

                    time.sleep(5)  # 每5秒更新一次

                except Exception as e:
                    engine.logger.error(f"监控线程出错: {str(e)}")
                    time.sleep(5)

            engine.logger.info("监控线程已停止")

    def run(self):
        """启动处理引擎"""
        self.logger.info("系统启动")

        # 创建输出目录
        os.makedirs(self.OUTPUT_DIR, exist_ok=True)

        # 创建线程
        producer = self.TaskProducer(self)
        processors = [self.TaskProcessor(self, i) for i in range(self.NUM_PROCESSORS)]
        writer = self.ResultWriter(self)
        monitor = self.Monitor(self)

        # 设置为守护线程
        producer.daemon = True
        for p in processors:
            p.daemon = True
        writer.daemon = True
        monitor.daemon = True

        # 启动线程
        producer.start()
        for p in processors:
            p.start()
        writer.start()
        monitor.start()

        try:
            # 等待生产者完成
            producer.join()
            self.all_producers_done = True
            self.logger.info("任务生产者已完成")

            # 等待处理队列清空
            self.task_queue.join()
            self.logger.info("所有任务已处理完毕")

            # 等待结果队列清空
            self.result_queue.join()
            self.logger.info("所有结果已写入完毕")

            # 等待监控线程结束
            monitor.join()

        except KeyboardInterrupt:
            self.logger.info("收到中断信号，准备安全退出")
            self.shutdown_system()

            # 等待所有线程安全退出
            self.logger.info("等待所有线程安全退出...")

            # 给线程一些时间来响应关闭信号
            time.sleep(2)

            # 清空所有队列，确保join()不会阻塞
            try:
                # 清空任务队列
                while not self.task_queue.empty():
                    try:
                        self.task_queue.get_nowait()
                        self.task_queue.task_done()
                    except:
                        break

                # 清空结果队列
                while not self.result_queue.empty():
                    try:
                        self.result_queue.get_nowait()
                        self.result_queue.task_done()
                    except:
                        break

            except Exception as e:
                self.logger.error(f"清空队列时出错: {str(e)}")

        finally:
            # 确保系统关闭
            self.shutdown_system()

            # 清理资源
            self.cleanup()

            # 输出最终统计
            with self.stats_lock:
                self.logger.info(f"最终统计 - 总任务: {self.stats['total_tasks']}, "
                               f"已处理: {self.stats['processed']}, "
                               f"成功: {self.stats['success']}, "
                               f"失败: {self.stats['failed']}, "
                               f"重试: {self.stats['retries']}")

            self.logger.info("系统已安全退出")

    def cleanup(self):
        """清理系统资源"""
        try:
            # 取消暂停定时器
            if self.pause_timer:
                self.pause_timer.cancel()
                self.pause_timer = None

            # 关闭HTTP会话
            if hasattr(self, 'session'):
                self.session.close()
                self.logger.info("HTTP会话已关闭")

        except Exception as e:
            self.logger.error(f"清理资源时出错: {str(e)}")

# 便捷启动函数
def run_synthesis(input_dir,
                  output_dir,
                  construct_prompt_func,
                  process_response_func,
                  config=None):
    """
    便捷启动合成引擎

    Args:
        input_dir: 输入目录
        output_dir: 输出目录
        construct_prompt_func: 构造提示词的函数
        process_response_func: 处理响应的函数
        config: 其他配置参数
    """
    if config is None:
        config = {}

    # 合并配置
    full_config = config.copy()
    full_config["INPUT_DIR"] = input_dir
    full_config["OUTPUT_DIR"] = output_dir

    # 创建并启动引擎
    engine = SynthesisEngine(
        config=full_config,
        construct_prompt_func=construct_prompt_func,
        process_response_func=process_response_func
    )

    engine.run()