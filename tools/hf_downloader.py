#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
HuggingFace资源下载工具：用于下载和保存HuggingFace上的数据集、模型和仓库快照。

主要功能：
- 支持下载并保存数据集（支持子集和分割选项）
- 支持下载预训练模型和对应的分词器
- 支持下载完整仓库快照

使用示例：
    downloader = HuggingFaceDownloader()
    # 下载数据集
    dataset = downloader.download(
        "dataset_name",
        DownloadType.DATASET,
        save_dir="data/datasets",
        subset="train"
    )

注意事项：
- 需要安装datasets、transformers和huggingface_hub库
- 下载大型模型时请确保有足够的磁盘空间
- 建议使用稳定的网络连接
"""

from datasets import load_dataset
from transformers import AutoTokenizer, AutoModelForMaskedLM
from huggingface_hub import snapshot_download
import argparse
import sys
import logging
import os
import json
from enum import Enum
from typing import Optional, Dict, Any

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class DownloadType(Enum):
    DATASET = "dataset"
    MODEL = "model"
    SNAPSHOT = "snapshot"

class HuggingFaceDownloader:
    def __init__(self, base_dir: str = "data"):
        self.base_dir = base_dir
        
    def download(self, 
                name: str, 
                download_type: DownloadType,
                save_dir: Optional[str] = None,
                **kwargs) -> Any:
        """统一的下载接口"""
        try:
            if save_dir:
                full_save_dir = os.path.join(self.base_dir, save_dir)
                os.makedirs(full_save_dir, exist_ok=True)
            else:
                full_save_dir = None
                
            download_funcs = {
                DownloadType.DATASET: self._download_dataset,
                DownloadType.MODEL: self._download_model,
                DownloadType.SNAPSHOT: self._download_snapshot
            }
            
            return download_funcs[download_type](name, full_save_dir, **kwargs)
            
        except Exception as e:
            logging.error(f"下载时发生错误: {str(e)}")
            sys.exit(1)
    
    def _download_dataset(self, 
                         dataset_name: str, 
                         save_dir: Optional[str],
                         subset: Optional[str] = None,
                         split: Optional[str] = None) -> Any:
        """下载数据集"""
        logging.info(f"开始下载数据集: {dataset_name}")
        dataset = load_dataset(dataset_name, subset, split=split)
        
        if save_dir:
            base_name = dataset_name.split('/')[-1]
            save_dir = os.path.join(save_dir, base_name)
            if not os.path.exists(save_dir):
                os.makedirs(save_dir, exist_ok=True)
                
            if subset:
                base_name += f"_{subset}"
            if split:
                base_name += f"_{split}"
            num_samples = len(dataset)
            save_path = os.path.join(save_dir, f"{base_name}_{num_samples}.jsonl")
            
            logging.info(f"保存数据集到: {save_path}")
            with open(save_path, 'w', encoding='utf-8') as f:
                for item in dataset:
                    f.write(json.dumps(item, ensure_ascii=False) + '\n')
                    
        return dataset
    
    def _download_model(self,
                       model_name: str,
                       save_dir: Optional[str]) -> tuple:
        """下载模型和分词器"""
        logging.info(f"开始下载模型: {model_name}")
        
        if save_dir:
            model_save_path = os.path.join(save_dir, model_name.split('/')[-1])
            os.makedirs(model_save_path, exist_ok=True)
        
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModelForMaskedLM.from_pretrained(
            model_name,
            torch_dtype="auto",
            trust_remote_code=True
        )
        
        if save_dir:
            tokenizer.save_pretrained(model_save_path)
            model.save_pretrained(model_save_path)
            
        return model, tokenizer
    
    def _download_snapshot(self,
                          repo_name: str,
                          save_dir: Optional[str]) -> str:
        """下载仓库快照"""
        logging.info(f"开始下载仓库快照: {repo_name}")
        
        if save_dir:
            repo_save_path = os.path.join(save_dir, repo_name.split('/')[-1])
        else:
            repo_save_path = None
            
        local_dir = snapshot_download(
            repo_id=repo_name,
            local_dir=repo_save_path,
            local_dir_use_symlinks=False
        )
        
        return local_dir

def main():
    parser = argparse.ArgumentParser(description='Hugging Face 资源下载工具')
    parser.add_argument('name', type=str, help='要下载的资源名称')
    parser.add_argument('--type', type=str, choices=['dataset', 'model', 'snapshot'],
                      required=True, help='下载类型')
    parser.add_argument('--save_dir', type=str, help='保存目录')
    parser.add_argument('--subset', type=str, help='数据集子集（仅用于数据集）')
    parser.add_argument('--split', type=str, help='数据集分割（仅用于数据集）')
    
    args = parser.parse_args()
    
    downloader = HuggingFaceDownloader()
    downloader.download(
        args.name,
        DownloadType(args.type),
        args.save_dir,
        subset=args.subset,
        split=args.split
    )

if __name__ == "__main__":
    # 示例用法
    downloader = HuggingFaceDownloader()
    
    # 下载数据集
    downloader.download(
        "Duxiaoman-DI/FinCorpus",
        DownloadType.DATASET,
        save_dir="source/fin-r1",
        subset="default",
        split="test"
    )
    
    # # 下载模型
    # downloader.download(
    #     "facebook/fasttext-zh-vectors",
    #     DownloadType.MODEL,
    #     save_dir="models"
    # ) 
    
    # # 下载仓库快照
    # downloader.download(
    #     "open-r1/OpenR1-Math-220k",
    #     DownloadType.SNAPSHOT,
    #     save_dir="source/reasoning"
    # )
    