#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
支付宝金融评测数据集处理模块

该模块提供了处理支付宝金融评测数据集的函数，包括蚂蚁和上财两部分数据。
主要功能：
- 处理蚂蚁金融评测数据集
- 处理上财金融评测数据集
- 将数据转换为标准格式

使用方法：
    在normalize_datasets.py中导入并使用process_ant_finance和process_sufe_finance函数
"""

import pandas as pd
import os
import json
import uuid
import glob
from preprocess.utils import ensure_directory, create_standard_format, save_jsonl

def load_dataset_Ant(path, dataset_type):
    """
    加载蚂蚁金融评测数据集

    Args:
        path: 数据集路径
        dataset_type: 数据集类型，可选值为"dev", "test", "full"

    Returns:
        list: 数据集列表
    """
    dataset = []
    if dataset_type not in ["dev", "test", "full"]:
        raise ValueError("加载数据集类型错误，限定在dev、test、full中，当前赋值为 ：" + str(dataset_type))

    for dirpath, dirnames, filenames in os.walk(path):
        for filename in [f for f in filenames if f.endswith(".csv")]:
            subject = dirpath.split("/")[-1]
            path = os.path.join(dirpath, filename)
            domain = filename.split(".")[0]
            frame = pd.read_csv(path)
            headers = frame.head(0)
            headers = list(headers)
            for i in range(len(frame)):
                tid = "/"
                context = "/"
                question = "/"
                A="/"
                B="/"
                C="/"
                D="/"
                E="/"
                answer = "/"

                if "id" in headers:
                    id = frame["id"][i]
                if "context" in headers:
                    context = frame["context"][i]
                if "question" in headers:
                    question = frame["question"][i]
                if "A" in headers:
                    A = frame["A"][i]
                if "B" in headers:
                    B = frame["B"][i]
                if "C" in headers:
                    C = frame["C"][i]
                if "D" in headers:
                    D = frame["D"][i]
                if "E" in headers:
                    E = frame["E"][i]

                if "answer" in headers:
                    answer = frame["answer"][i]
                    if answer != answer:  # 处理NaN值
                        answer = ""

                # 跳过没有答案的数据
                if answer == "":
                    continue

                # 判断加载数据集类型，其中
                # dev集是披露answer的评测题
                # test集是未披露answer的评测题
                # full集是所有评测题
                if dataset_type == "dev" and answer == "":
                    continue
                elif dataset_type == "test" and answer != "":
                    continue
                elif dataset_type == "full":
                    pass

                # 根据不同领域构建不同的prompt
                if domain == "会计从业资格考试":
                    system_content = "你是一名专业的会计从业人员，你需要从A、B、C、D四个选项中选出一个作为回答问题的最恰当的答案，你只能输出一个字符，并且这个字符是A、B、C、D中一个。"
                    user_content = f"""
会计从业资格考试问题是：{question}
选项：
A.{A}
B.{B}
C.{C}
D.{D}
"""
                elif domain == "金融文档抽取":
                    system_content = "你是一名专业的金融从业者，你会收到一份资讯内容，以及一个问题内容，你需要从A、B、C、D四个选项中选出一个作为该问题内容最恰当的回答，你只能输出一个字符，并且这个字符是A、B、C、D中一个。"
                    user_content = f"""
金融资讯：{context}
问题：{question}
选项：
A.{A}
B.{B}
C.{C}
D.{D}
"""
                elif domain == "保险知识解读":
                    system_content = "你是一名专业的保险专家，你对任何保险知识都了解，你需要从A、B、C、D四个选项中选出一个作为问题最恰当的回答，你只能输出一个字符，并且这个字符是A、B、C、D中一个。"
                    user_content = f"""
保险问题：{question}
选项：
A.{A}
B.{B}
C.{C}
D.{D}
"""
                elif domain == "理财知识解读":
                    system_content = "你是一名专业的理财专家，你对任何理财知识都了解，你需要从A、B、C、D四个选项中选出一个作为问题最恰当的回答，你只能输出一个字符，并且这个字符是A、B、C、D中一个。"
                    user_content = f"""
理财问题：{question}
选项：
A.{A}
B.{B}
C.{C}
D.{D}
"""
                elif domain == "金融术语解释":
                    system_content = "你是一名专业的金融专家，你对任何金融术语都了解，你需要从A、B、C、D四个选项中选出一个作为术语最恰当的描述，你只能输出一个字符，并且这个字符是A、B、C、D中一个。"
                    user_content = f"""
金融术语：{question}
选项：
A.{A}
B.{B}
C.{C}
D.{D}
"""
                elif domain == "执业药师资格考试":
                    system_content = "你是一名专业的执业药师人员，你需要从A、B、C、D、E五个选项中选出一个作为回答问题的最恰当的答案，你只能输出一个字符，并且这个字符是A、B、C、D、E中一个。"
                    user_content = f"""执业药师资格考试问题是：{question}
选项：
A.{A}
B.{B}
C.{C}
D.{D}
E.{E}
"""
                elif domain == "执业医师资格考试":
                    system_content = "你是一名专业的执业医师人员，你需要从A、B、C、D、E五个选项中选出一个作为回答问题的最恰当的答案，你只能输出一个字符，并且这个字符是A、B、C、D、E中一个。"
                    user_content = f"""执业医师资格考试问题是：{question}
选项：
A.{A}
B.{B}
C.{C}
D.{D}
E.{E}
"""
                elif domain == "保险从业资格考试":
                    system_content = "你是一名专业的保险从业人员，你需要从A、B、C、D四个选项中选出一个作为回答问题的最恰当的答案，你只能输出一个字符，并且这个字符是A、B、C、D中一个。"
                    user_content = f"""基金从业资格考试问题是：{question}
选项：
A.{A}
B.{B}
C.{C}
D.{D}
"""
                elif domain == "基金从业资格考试":
                    system_content = "你是一名专业的基金从业人员，你需要从A、B、C、D四个选项中选出一个作为回答问题的最恰当的答案，你只能输出一个字符，并且这个字符是A、B、C、D中一个。"
                    user_content = f"""基金从业资格考试问题是：{question}
选项：
A.{A}
B.{B}
C.{C}
D.{D}
"""
                elif domain == "证券从业资格考试":
                    system_content = "你是一名专业的证券从业人员，你需要从A、B、C、D四个选项中选出一个作为回答问题的最恰当的答案，你只能输出一个字符，并且这个字符是A、B、C、D中一个。"
                    user_content = f"""证券从业资格考试问题是：{question}
选项：
A.{A}
B.{B}
C.{C}
D.{D}
"""
                elif domain == "银行从业资格考试":
                    system_content = "你是一名专业的银行从业人员，你需要从A、B、C、D四个选项中选出一个作为回答问题的最恰当的答案，你只能输出一个字符，并且这个字符是A、B、C、D中一个。"
                    user_content = f"""银行从业资格考试问题是：{question}
选项：
A.{A}
B.{B}
C.{C}
D.{D}
"""
                elif domain == "期货从业资格考试":
                    system_content = "你是一名专业的期货从业人员，你需要从A、B、C、D四个选项中选出一个作为回答问题的最恰当的答案，你只能输出一个字符，并且这个字符是A、B、C、D中一个。"
                    user_content = f"""期货从业资格考试问题是：{question}
选项：
A.{A}
B.{B}
C.{C}
D.{D}
"""
                elif domain == "期权从业人员考试":
                    system_content = "你是一名专业的期权从业人员，你需要从A、B、C、D四个选项中选出一个作为回答问题的最恰当的答案，你只能输出一个字符，并且这个字符是A、B、C、D中一个。"
                    user_content = f"""期权从业人员考试问题是：{question}
选项：
A.{A}
B.{B}
C.{C}
D.{D}
"""
                elif domain == "注册税务师":
                    system_content = "你是一名专业的注册税务师，你需要从A、B、C、D四个选项中选出一个作为回答问题的最恰当的答案，你只能输出一个字符，并且这个字符是A、B、C、D中一个。"
                    user_content = f"""注册税务师资格考试问题是：{question}
选项：
A.{A}
B.{B}
C.{C}
D.{D}
"""
                elif domain == "审计师考试":
                    system_content = "你是一名专业的审计师，你需要从A、B、C、D四个选项中选出一个作为回答问题的最恰当的答案，你只能输出一个字符，并且这个字符是A、B、C、D中一个。"
                    user_content = f"""审计师考试问题是：{question}
选项：
A.{A}
B.{B}
C.{C}
D.{D}
"""
                elif domain == "金融意图理解":
                    system_content = "你是一名专业的金融专家，你可以对用户提问的意图进行理解，你需要从A、B、C、D四个选项中选出一个作为回答问题的最恰当的答案，你只能输出一个字符，并且这个字符是A、B、C、D中一个。"
                    user_content = f"""用户的提问是：{question}
选项：
A.{A}
B.{B}
C.{C}
D.{D}
"""
                elif domain == "保险意图理解":
                    system_content = "你是一名专业的保险顾问，你可以对用户提问的意图进行理解，你需要从A、B、C、D四个选项中选出一个作为回答问题的最恰当的答案，你只能输出一个字符，并且这个字符是A、B、C、D中一个。"
                    user_content = f"""用户的提问是：{question}
选项：
A.{A}
B.{B}
C.{C}
D.{D}
"""
                elif domain == "金融槽位识别":
                    system_content = "你是一位金融领域的专家，你可以从一段文本中识别出其中特定的公司、政府主体。你需要从A、B、C、D四个选项中选出一个作为主体识别的最恰当的答案，你只能输出一个字符，并且这个字符是A、B、C、D中一个。"
                    user_content = f"""这是你收到的文本信息：{question}
选项：
A.{A}
B.{B}
C.{C}
D.{D}
"""
                elif domain == "保险槽位识别":
                    system_content = "你是一位保险领域的专家，你可以从一段文本中识别出其中特定的险种、保险产品名词。你需要从A、B、C、D四个选项中选出一个作为识别的最恰当的答案，你只能输出一个字符，并且这个字符是A、B、C、D中一个。"
                    user_content = f"""这是你收到的文本信息：{question}
选项：
A.{A}
B.{B}
C.{C}
D.{D}
"""
                elif domain == "金融情绪识别":
                    system_content = "你是一名专业的金融分析师，你能够识别出一段金融文本的多种情绪分类：积极、中性、消极。接下来你会得到一段金融文本，你需要判断该文本所表达的情绪是属于积极、中性、消极的哪一类别。你需要从A、B、C三个选项中选出一个作为识别的最恰当的答案，你只能输出一个字符，并且这个字符是A、B、C中一个。"
                    user_content = f"""金融文本是：{question}
选项：
A.{A}
B.{B}
C.{C}
"""
                elif domain == "研判观点提取":
                    system_content = "你是一位金融领域的专家，你能够识别出一段研报文本反映的市场走向：利空、中性、利好、未知。接下来你会得到一段研报文本，你需要判断该文本所反映的市场走向是属于利空、中性、利好、未知的哪一类别。你需要从A、B、C、D四个选项中选出一个作为识别的最恰当的答案，你只能输出一个字符，并且这个字符是A、B、C、D中一个。"
                    user_content = f"""研报内容是：
{question}
以下哪个选项可以最恰当的描述研报的研判观点？
A.{A}
B.{B}
C.{C}
D.{D}
"""
                elif domain == "金融数值计算":
                    system_content = "你是数值计算的专家，你可以完成金融数值计算的题目。你需要从A、B、C、D四个选项中选出一个作为回答问题的最恰当的答案，你只能输出一个字符，并且这个字符是A、B、C、D中一个。"
                    user_content = f"""题目：{question}
选项：
A.{A}
B.{B}
C.{C}
D.{D}
"""
                elif domain == "金融产品分析":
                    system_content = "你是金融产品的专家，你会收到一段基金的描述，然后你需要回答关于基金描述的问题。你需要从A、B、C、D四个选项中选出一个作为回答问题的最恰当的答案，你只能输出一个字符，并且这个字符是A、B、C、D中一个。"
                    user_content = f"""基金描述：
{context}
问题：{question}
选项：
A.{A}
B.{B}
C.{C}
D.{D}
"""
                elif domain == "金融事件解读":
                    system_content = "你是金融事件解读的专家，你会收到一段金融事件内容，然后你需要回答关于该金融事件解读的问题，你需要从A、B、C、D四个选项中选出一个作为回答问题的最恰当的答案，你只能输出一个字符，并且这个字符是A、B、C、D中一个。"
                    user_content = f"""资讯内容：
{context}
问题：{question}
选项：
A.{A}
B.{B}
C.{C}
D.{D}
"""
                elif domain == "保险属性抽取":
                    system_content = "你是保险属性抽取的专家，请根据提取要求，给出正确答案的选项，请选出其中最恰当的答案选项，你需要从A、B、C、D四个选项中选出一个作为回答问题的最恰当的答案，你只能输出一个字符，并且这个字符是A、B、C、D中一个。"
                    user_content = f"""保险条款内容：{context}
提取要求：{question}
选项：
A.{A}
B.{B}
C.{C}
D.{D}
"""
                elif domain == "保险条款解读":
                    system_content = "你是保险条款解读的专家，你会收到一段保险条款内容，然后你需要回答关于该条款内容的问题，你需要从A、B、C、D四个选项中选出一个作为回答问题的最恰当的答案，你只能输出一个字符，并且这个字符是A、B、C、D中一个。"
                    user_content = f"""保险条款内容：
{context}
问题：{question}
选项：
A.{A}
B.{B}
C.{C}
D.{D}
"""
                elif domain == "保险核赔推理":
                    system_content = "你是一名专业的保险核赔审核专家，假设投保人的其他赔付条件都满足，现基于患者的既往史、现病史、主诉、诊断信息判断该保险案例是否符合保险理赔赔付。你的输出只能是\"是\"或者\"否\"。"
                    user_content = f"""案例内容：
{question}
"""
                elif domain == "文本总结归纳":
                    system_content = "你是一名专业的金融专家，你需要对给定的文本进行总结归纳。你需要从A、B、C、D四个选项中选出一个作为文本总结归纳的最恰当的答案，你只能输出一个字符，并且这个字符是A、B、C、D中一个。"
                    user_content = f"""文本内容是：
{question}
以下哪个选项可以最恰当的对文本内容进行归纳？
A.{A}
B.{B}
C.{C}
D.{D}
"""
                elif domain == "资讯标题生成":
                    system_content = "你是一名专业的金融专家，你需要对给定的文本进行标题生成。你需要从A、B、C、D四个选项中选出一个作为标题生成的最恰当的答案，你只能输出一个字符，并且这个字符是A、B、C、D中一个。"
                    user_content = f"""文本内容是：
{question}
以下哪个选项可以最恰当的作为文本的标题？
A.{A}
B.{B}
C.{C}
D.{D}
"""
                elif domain == "投教话术生成":
                    system_content = "你是一位金融领域的专家，你会收到一个简短词汇（金融术语等），你可以生成文本让用户更好理解这个词的含义。你需要从A、B、C、D四个选项中选出一个作为话术生成的最恰当的答案，你只能输出一个字符，并且这个字符是A、B、C、D中一个。"
                    user_content = f"""金融词汇：{question}
以下哪个选项可以最恰当得让用户更好理解这个词的含义？
A.{A}
B.{B}
C.{C}
D.{D}
"""
                elif domain == "营销文案生成":
                    system_content = "你是一名专业的金融营销专家，你可以基于一个产品的名称、功能介绍、产品特点为它设定一句营销文案。你需要从A、B、C、D四个选项中选出一个作为营销文案生成的最恰当的答案，你只能输出一个字符，并且这个字符是A、B、C、D中一个。"
                    user_content = f"""产品内容是：
{question}
以下哪个选项可以最恰当的作为该产品的营销文案？
A.{A}
B.{B}
C.{C}
D.{D}
"""
                elif domain == "金融合规性":
                    system_content = "你是一名专业的金融行业金融合规审核员，你可以判断给定的输入包含的信息是否金融合规。"
                    user_content = f"""问题是：{question}
你的输出只能是"是"或者"否\""""
                elif domain == "金融问题识别":
                    system_content = "你是一名专业的金融行业安全审核员，你可以判断给定的问题是否属于金融问题。"
                    user_content = f"""问题是：{question}
你的输出只能是"是"或者"否\""""
                elif domain == "信息安全合规":
                    system_content = "你是一名专业的金融行业信息安全审核员，你可以判断给定的输入包含的信息是否安全合规。"
                    user_content = f"""问题是：{question}
你的输出只能是"是"或者"否\""""
                elif domain == "金融事实性":
                    system_content = "你是一名专业的金融行业金融信息事实性审核员，你可以判断给定的输入包含的信息是否符合事实。"
                    user_content = f"""问题是：{question}
你的输出只能是"是"或者"否\""""
                else:
                    # 默认prompt格式
                    system_content = "你是一名专业的金融专家，你需要从A、B、C、D四个选项中选出一个作为问题最恰当的回答，你只能输出一个字符，并且这个字符是A、B、C、D中一个。"
                    user_content = f"""问题：{question}
选项：
A.{A}
B.{B}
C.{C}
D.{D}
"""

                details = {}
                details["id"] = int(id)
                details["subject"] = subject
                details["domain"] = domain
                details["system_content"] = system_content
                details["user_content"] = user_content
                details["answer"] = answer
                dataset.append(details)
    return dataset

def split_standard(domain_name):
    """
    标准化领域名称，去除后缀

    Args:
        domain_name: 领域名称

    Returns:
        str: 标准化后的领域名称
    """
    targets = ["_dev", "_val", "_test"]
    for item in targets:
        if domain_name.endswith(item):
            domain_name = domain_name.split(item)[0]
    return domain_name

def load_dataset_SUFE(path, dataset_type):
    """
    加载上财金融评测数据集

    Args:
        path: 数据集路径
        dataset_type: 数据集类型，可选值为"dev", "val", "full"

    Returns:
        list: 数据集列表
    """
    dataset = []

    # dataset_type="dev" 对应上财版本的dev
    # dataset_type="val" 对应上财版本的val
    # dataset_type="full" 对应上财版本的dev+val
    if dataset_type not in ["dev", "val", "full"]:
        raise ValueError("加载数据集类型错误，限定在dev、val、full中，当前赋值为 ：" + str(dataset_type))

    if not os.path.exists(os.path.join(path, "subject_map.json")):
        raise ValueError("subject_map.json 不在 " + path + " 路径下")
    f = open(os.path.join(path, "subject_map.json"), "r")
    dicts = json.load(f)
    f.close()

    # 根据dataset_type确定要处理的目录
    dirs_to_process = []
    if dataset_type == "dev":
        dirs_to_process = ["dev"]
    elif dataset_type == "val":
        dirs_to_process = ["val"]
    elif dataset_type == "full":
        dirs_to_process = ["dev", "val"]

    for dir_name in dirs_to_process:
        dir_path = os.path.join(path, dir_name)
        if not os.path.exists(dir_path):
            print(f"警告：目录 {dir_path} 不存在，跳过处理")
            continue

        for filename in [f for f in os.listdir(dir_path) if f.endswith(".csv")]:
            file_path = os.path.join(dir_path, filename)
            domain_name = split_standard(filename.split(".")[0])

            # 跳过subject_map.json
            if domain_name not in dicts:
                continue

            subject = dicts[domain_name]["subject"]
            domain = dicts[domain_name]["ch"]
            frame = pd.read_csv(file_path)
            headers = frame.head(0)
            headers = list(headers)
            for i in range(len(frame)):
                tid = "/"
                context = "/"
                question = "/"
                A="/"
                B="/"
                C="/"
                D="/"
                E="/"
                answer = "/"
                explanation = None

                if "id" in headers:
                    id = frame["id"][i]
                if "question" in headers:
                    question = frame["question"][i]
                if "A" in headers:
                    A = frame["A"][i]
                if "B" in headers:
                    B = frame["B"][i]
                if "C" in headers:
                    C = frame["C"][i]
                if "D" in headers:
                    D = frame["D"][i]

                if "answer" in headers:
                    answer = frame["answer"][i]
                    if answer != answer:  # 处理NaN值
                        answer = ""
                else:
                    answer = ""

                # 处理解释字段（仅dev目录下的文件包含）
                if "explanation" in headers:
                    explanation = frame["explanation"][i]
                    if explanation != explanation:  # 处理NaN值
                        explanation = None

                # 跳过没有答案的数据
                if answer == "":
                    continue

                system_content = f"以下是中国关于{domain}考试的单项选择题，请选出其中的正确答案。"
                user_content = f"""{question}
A.{A}
B.{B}
C.{C}
D.{D}
"""
                details = {}
                details["id"] = int(id)
                details["subject"] = subject
                details["domain"] = domain
                details["system_content"] = system_content
                details["user_content"] = user_content
                details["answer"] = answer

                # 如果有解释字段，添加到details中
                if explanation is not None:
                    details["explanation"] = explanation

                dataset.append(details)

    return dataset

def load_dataset(path, dataset_type):
    """
    加载完整的金融评测数据集

    Args:
        path: 数据集路径
        dataset_type: 数据集类型，可选值为"dev", "test", "full"

    Returns:
        list: 数据集列表
    """
    dataset = load_dataset_Ant(os.path.join(path, "Ant"), dataset_type)
    dataset.extend(load_dataset_SUFE(os.path.join(path, "SUFE"), dataset_type))
    return dataset

def convert_to_standard_format(item):
    """
    将数据项转换为标准格式

    Args:
        item: 数据项，包含 system_content, user_content 和 answer 字段
              可选包含 explanation 字段

    Returns:
        dict: 标准格式的数据项，包含以下字段：
            - id: 唯一标识符
            - conversations: 对话列表，包含 system, user, assistant 角色的内容
                - system: 系统提示，包含角色描述和要求将答案放在 \boxed{} 中
                - user: 用户问题
                - assistant: 助手回答，包含 reasoning 和 final_answer 字段
                  如果有 explanation，则 content 为 "{explanation} 因此，最终答案是：\boxed{answer}"
            - meta: 元数据，包含 language 和 task_desc 字段
    """
    # 获取系统提示并添加要求将答案放在 \boxed{} 中
    system_content = item.get("system_content", "")
    if system_content and not "\\boxed{}" in system_content:
        system_content = system_content.rstrip() + " 请将答案放在\\boxed{}格式中。"

    # 确保答案使用 \boxed{} 包裹
    answer = item["answer"]

    # 构建assistant_content
    explanation = item.get("explanation")
    if explanation:
        # 如果有解释字段，拼接为 "{explanation} 因此，最终答案是：\boxed{answer}"
        assistant_content = f"{explanation} 因此，最终答案是：\\boxed{{{answer}}}"
    else:
        # 如果没有解释字段，直接使用boxed_answer
        assistant_content = f"答案是：\\boxed{{{answer}}}"

    # 确定语言和任务描述
    language = "zh"  # 蚂蚁金融和上财数据集都是中文

    # 根据domain确定任务描述
    domain = item.get("domain", "")
    if "考试" in domain or "资格" in domain:
        task_desc = f"金融领域{domain}选择题"
    elif "理解" in domain:
        task_desc = f"金融领域{domain}任务"
    elif "识别" in domain:
        task_desc = f"金融领域{domain}任务"
    elif "解读" in domain:
        task_desc = f"金融领域{domain}任务"
    elif "分析" in domain:
        task_desc = f"金融领域{domain}任务"
    elif "抽取" in domain:
        task_desc = f"金融领域{domain}任务"
    elif "生成" in domain:
        task_desc = f"金融领域{domain}任务"
    elif "合规" in domain:
        task_desc = f"金融领域{domain}判断任务"
    else:
        task_desc = f"金融领域{domain}问答任务"

    # 使用utils.py中的create_standard_format函数
    from preprocess.utils import create_standard_format
    return create_standard_format(
        system_content=system_content,
        user_content=item.get("user_content", item.get("prompt", "")),
        assistant_content=assistant_content,
        reasoning="",
        final_answer=answer,
        language=language,
        task_desc=task_desc
    )

def save_jsonl(data, file_path):
    """
    保存数据为JSONL格式，文件名中包含数据行数

    Args:
        data: 数据列表
        file_path: 保存路径
    """
    # 在文件名中添加行数
    base_dir = os.path.dirname(file_path)
    file_name = os.path.basename(file_path)

    # 分离文件名和扩展名
    name_parts = file_name.split('.')
    base_name = name_parts[0]
    ext = '.'.join(name_parts[1:])

    # 检查文件名是否已经包含行数
    if '_' in base_name and base_name.split('_')[-1].isdigit():
        # 已经包含行数，替换它
        name_parts = base_name.split('_')
        name_parts[-1] = str(len(data))
        new_base_name = '_'.join(name_parts)
    else:
        # 不包含行数，添加它
        new_base_name = f"{base_name}_{len(data)}"

    # 构建新的文件路径
    new_file_path = os.path.join(base_dir, f"{new_base_name}.{ext}")

    ensure_directory(base_dir)
    with open(new_file_path, 'w', encoding='utf-8') as f:
        for item in data:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')

    print(f"已保存 {len(data)} 条数据到 {new_file_path}")

def merge_and_save_ant_data():
    """
    合并并保存蚂蚁部分数据
    """
    print("合并蚂蚁部分数据...")
    # 加载所有数据（包括有答案和无答案的）
    ant_dataset = load_dataset_Ant("data/source/fin-r1/ant_finance/Ant", "full")
    print(f"蚂蚁部分数据总数：{len(ant_dataset)}")

    # 转换为标准格式
    standardized_data = [convert_to_standard_format(item) for item in ant_dataset]

    # 保存为JSONL文件
    output_path = "data/processed/fin-r1/ant_finance/ant_finance.jsonl"
    save_jsonl(standardized_data, output_path)

def merge_and_save_sufe_data():
    """
    合并并保存上财部分数据
    """
    print("合并上财部分数据...")

    # 使用load_dataset_SUFE函数加载数据
    sufe_base_path = "data/source/fin-r1/ant_finance/SUFE"

    # 加载dev数据（包含答案和解释）
    dev_dataset = load_dataset_SUFE(sufe_base_path, "dev")

    # test数据无答案

    # 加载val数据（包含答案）
    val_dataset = load_dataset_SUFE(sufe_base_path, "val")

    print(f"上财部分数据总数：dev={len(dev_dataset)}, val={len(val_dataset)}")

    # 转换为标准格式并保存
    if dev_dataset:
        standardized_dev = [convert_to_standard_format(item) for item in dev_dataset]
        dev_output_path = "data/processed/fin-r1/ant_finance_SUFE/sufe_dev.jsonl"
        save_jsonl(standardized_dev, dev_output_path)

    if val_dataset:
        standardized_val = [convert_to_standard_format(item) for item in val_dataset]
        val_output_path = "data/processed/fin-r1/ant_finance_SUFE/sufe_val.jsonl"
        save_jsonl(standardized_val, val_output_path)

if __name__ == "__main__":
    print("开始处理支付宝金融评测数据集...")
    print("="*50)

    # 合并并保存数据
    merge_and_save_ant_data()
    print("-"*50)
    merge_and_save_sufe_data()

    print("="*50)
    print("支付宝金融评测数据集处理完成！")
    print("数据已保存到 data/source/fin-r1 目录下")
