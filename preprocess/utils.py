import os
import json
import uuid
import re
import pandas as pd
from typing import Dict, List, Any, Optional, Union, Tuple

def ensure_directory(directory: str) -> None:
    """确保目录存在，如果不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)

def read_jsonl(file_path: str) -> List[Dict[str, Any]]:
    """读取JSONL文件"""
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            if line.strip():  # 跳过空行
                data.append(json.loads(line))
    return data

def read_json(file_path: str) -> Union[List[Dict[str, Any]], Dict[str, Any]]:
    """读取JSON文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def read_parquet(file_path: str) -> pd.DataFrame:
    """读取Parquet文件"""
    return pd.read_parquet(file_path)

def update_filename_with_count(file_path: str, count: int) -> str:
    """更新文件名，添加样本数量统计

    如果文件名已有样本数量统计后缀（如 _123.jsonl），则先去掉，再添加新的统计

    Args:
        file_path: 原文件路径
        count: 样本数量

    Returns:
        str: 更新后的文件路径
    """
    # 分离文件路径、文件名和扩展名
    dir_name = os.path.dirname(file_path)
    base_name = os.path.basename(file_path)

    # 检查文件名是否已有样本数量统计后缀
    # 匹配模式：文件名_数字.扩展名
    match = re.match(r'(.+)_(\d+)(\..+)$', base_name)
    if match:
        # 如果有，提取原始文件名和扩展名
        file_name = match.group(1)
        extension = match.group(3)
    else:
        # 如果没有，正常分离文件名和扩展名
        file_name, extension = os.path.splitext(base_name)

    # 构建新的文件名，添加样本数量统计（纯数字）
    new_file_name = f"{file_name}_{count}{extension}"

    # 组合新的文件路径
    if dir_name:
        return os.path.join(dir_name, new_file_name)
    else:
        return new_file_name

def save_jsonl(data: List[Dict[str, Any]], file_path: str) -> None:
    """保存为JSONL文件，并在文件名中添加样本数量统计

    Args:
        data: 要保存的数据列表
        file_path: 保存的文件路径
    """
    # 统计样本数量
    sample_count = len(data)

    # 更新文件名，添加样本数量统计
    updated_file_path = update_filename_with_count(file_path, sample_count)

    # 确保目录存在
    ensure_directory(os.path.dirname(updated_file_path))

    # 保存数据
    with open(updated_file_path, 'w', encoding='utf-8') as f:
        for item in data:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')

    print(f"已保存 {sample_count} 条数据到文件: {updated_file_path}")

def extract_boxed_content(text: str) -> Optional[str]:
    """从文本中提取 \boxed{xxx} 或 \boxed{{xxx}} 中的内容，如果有多个，取最后一个"""
    # 先尝试匹配 \boxed{{xxx}}
    matches = re.findall(r'\\boxed{{([^}]*)}}', text)
    if matches:
        return matches[-1]  # 返回最后一个匹配

    # 如果没有匹配到，尝试匹配 \boxed{xxx}
    matches = re.findall(r'\\boxed{([^}]*)}', text)
    if matches:
        return matches[-1]  # 返回最后一个匹配

    return None

def extract_tagged_content(text: str, tag: str) -> Optional[str]:
    """从文本中提取被标签包裹的内容，如 <tag>xxx</tag>"""
    pattern = f"<{tag}>(.*?)</{tag}>"
    match = re.search(pattern, text, re.DOTALL)
    if match:
        return match.group(1).strip()
    return None

def create_standard_format(
    system_content: str = "",
    user_content: str = "",
    assistant_content: str = "",
    reasoning: str = "",
    final_answer: str = "",
    language: str = "zh",
    task_desc: str = "",
    task_type: str = ""
) -> Dict[str, Any]:
    """创建标准格式的数据

    Args:
        system_content: 系统提示内容
        user_content: 用户问题内容
        assistant_content: 助手回答内容
        reasoning: 推理过程
        final_answer: 最终答案
        language: 数据语言，默认为"zh"（中文）
        task_desc: 任务描述
        task_type: 任务类型

    Returns:
        Dict: 标准格式的数据，包含id、conversations和meta字段
    """
    # 去掉内容前后可能存在的空格或换行符，标准化处理
    system_content = system_content.strip()
    user_content = user_content.strip()
    assistant_content = assistant_content.strip()
    reasoning = reasoning.strip()
    final_answer = final_answer.strip()
    task_desc = task_desc.strip()
    task_type = task_type.strip()

    return {
        "id": str(uuid.uuid4()),
        "conversations": [
            {"role": "system", "content": system_content},
            {"role": "user", "content": user_content},
            {"role": "assistant", "content": assistant_content, "reasoning": reasoning, "final_answer": final_answer}
        ],
        "meta": {
            "language": language,
            "task_desc": task_desc,
            "task_type": task_type
        }
    }
