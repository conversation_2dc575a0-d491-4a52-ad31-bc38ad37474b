#!/usr/bin/env python3
"""
数据集规范化脚本
用于处理data/source目录下的数据集格式标准化
处理后的数据放入data/processed下与原始数据集相同子路径和名称

使用方法:
    python -m preprocess.normalize_datasets [--config CONFIG_PATH]
"""

import os
import glob
import logging
import argparse
import yaml
from typing import Callable, Dict, Any
from preprocess.utils import ensure_directory
from preprocess.processors import (
    process_dianjin_r1,
    process_finance_instruct_500k,
    process_financeiq,
    process_fincuge_instruction,
    process_quant_trading_instruct,
    process_twitter_financial_news_sentiment,
    process_finqa,
    process_fincorpus,
    process_convfinqa
)
from preprocess.ant_finance_processor import merge_and_save_ant_data, merge_and_save_sufe_data

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 源数据和目标数据目录
SOURCE_DIR = "data/source"
TARGET_DIR = "data/processed"

# 数据集处理函数映射
PROCESSORS = {
    # 精确文件匹配
    "dianjin-r1/cflue_mcq.json": process_dianjin_r1,
    "dianjin-r1/cflue_oe.json": process_dianjin_r1,
    "dianjin-r1/fin_qa.json": process_dianjin_r1,

    # 目录匹配
    "fin-r1/Finance-Instruct-500k": process_finance_instruct_500k,
    "fin-r1/FinanceIQ": process_financeiq,
    "fin-r1/FinCUGE-Instruction": process_fincuge_instruction,
    "fin-r1/Quant-Trading-Instruct": process_quant_trading_instruct,
    "fin-r1/twitter-financial-news-sentiment": process_twitter_financial_news_sentiment,
    "fin-r1/finqa": process_finqa,
    "fin-r1/FinCorpus": process_fincorpus,
    "fin-r1/fingpt-convfinqa": process_convfinqa
}

def get_processor(file_path: str) -> Callable:
    """根据文件路径获取对应的处理函数"""
    # 完整路径匹配
    rel_path = os.path.relpath(file_path, SOURCE_DIR)
    for pattern, processor in PROCESSORS.items():
        if rel_path == pattern:
            return processor

    # 目录匹配
    dir_path = os.path.dirname(rel_path)
    for pattern, processor in PROCESSORS.items():
        if dir_path == pattern or dir_path.startswith(pattern):
            return processor

    # 默认处理函数
    raise ValueError(f"未找到文件 {file_path} 的处理函数")

def process_file(file_path: str) -> None:
    """处理单个文件"""
    try:
        # 获取相对路径
        rel_path = os.path.relpath(file_path, SOURCE_DIR)
        # 构建目标文件路径
        target_path = os.path.join(TARGET_DIR, rel_path)

        # 如果目标文件是JSON或Parquet，转换为JSONL
        base, ext = os.path.splitext(target_path)
        if ext.lower() in ['.json', '.parquet']:
            target_path = base + '.jsonl'

        # 确保目标目录存在
        ensure_directory(os.path.dirname(target_path))

        # 获取处理函数
        processor = get_processor(file_path)

        # 处理文件
        logger.info(f"处理文件: {file_path} -> {target_path}")
        processor(file_path, target_path)
        logger.info(f"处理完成: {target_path}")

    except Exception as e:
        logger.error(f"处理文件 {file_path} 时出错: {str(e)}")

def process_directory(directory: str, process_only=None) -> None:
    """处理目录下的所有文件

    Args:
        directory: 要处理的目录
        process_only: 如果提供，只处理这些路径下的文件
    """
    # 处理JSON文件
    for file_path in glob.glob(os.path.join(directory, "**/*.json"), recursive=True):
        # 如果指定了process_only，检查文件是否在指定路径下
        if process_only:
            rel_path = os.path.relpath(file_path, SOURCE_DIR)
            if not any(rel_path == pattern or rel_path.startswith(pattern + "/") for pattern in process_only):
                continue
        process_file(file_path)

    # 处理JSONL文件
    for file_path in glob.glob(os.path.join(directory, "**/*.jsonl"), recursive=True):
        # 如果指定了process_only，检查文件是否在指定路径下
        if process_only:
            rel_path = os.path.relpath(file_path, SOURCE_DIR)
            if not any(rel_path == pattern or rel_path.startswith(pattern + "/") for pattern in process_only):
                continue
        process_file(file_path)

    # 处理Parquet文件
    for file_path in glob.glob(os.path.join(directory, "**/*.parquet"), recursive=True):
        # 如果指定了process_only，检查文件是否在指定路径下
        if process_only:
            rel_path = os.path.relpath(file_path, SOURCE_DIR)
            if not any(rel_path == pattern or rel_path.startswith(pattern + "/") for pattern in process_only):
                continue
        process_file(file_path)

def load_config(config_path: str) -> Dict[str, Any]:
    """加载配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        logger.error(f"加载配置文件 {config_path} 时出错: {str(e)}")
        return {}

def main() -> None:
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="数据集规范化处理脚本")
    parser.add_argument("--config", type=str, default="config/preprocess_config.yaml", help="配置文件路径")
    args = parser.parse_args()

    # 加载配置
    config = load_config(args.config)

    # 更新配置
    global SOURCE_DIR, TARGET_DIR
    SOURCE_DIR = config.get("SOURCE_DIR", SOURCE_DIR)
    TARGET_DIR = config.get("TARGET_DIR", TARGET_DIR)

    logger.info("开始处理数据集")
    logger.info(f"源数据目录: {SOURCE_DIR}")
    logger.info(f"目标数据目录: {TARGET_DIR}")

    # 确保目标目录存在
    ensure_directory(TARGET_DIR)

    # 处理特殊数据集
    if config.get("PROCESS_ANT_FINANCE", True):
        logger.info("处理蚂蚁金融评测数据集...")
        try:
            merge_and_save_ant_data()
            logger.info("蚂蚁金融评测数据集处理完成")
        except Exception as e:
            logger.error(f"处理蚂蚁金融评测数据集时出错: {str(e)}")

    if config.get("PROCESS_SUFE_FINANCE", True):
        logger.info("处理上财金融评测数据集...")
        try:
            merge_and_save_sufe_data()
            logger.info("上财金融评测数据集处理完成")
        except Exception as e:
            logger.error(f"处理上财金融评测数据集时出错: {str(e)}")

    # 处理其他数据集
    logger.info("处理其他数据集...")
    process_only = config.get("PROCESS_ONLY")
    if process_only:
        logger.info(f"只处理以下路径下的数据集: {process_only}")
    process_directory(SOURCE_DIR, process_only)

    logger.info("数据集处理完成")

if __name__ == "__main__":
    main()
