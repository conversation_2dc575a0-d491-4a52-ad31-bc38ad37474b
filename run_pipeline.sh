#!/bin/bash

# 金融数据处理流程执行脚本
# 用于方便地选择执行哪个流程，或者按顺序执行全部流程

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的标题
print_title() {
    echo -e "${BLUE}===================================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}===================================================${NC}"
}

# 打印带颜色的信息
print_info() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

# 打印带颜色的警告
print_warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

# 打印带颜色的错误
print_error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

# 检查Python环境
check_python() {
    if ! command -v python &> /dev/null; then
        print_error "Python未安装，请先安装Python"
        exit 1
    fi

    print_info "Python环境检查通过"
}

# 检查必要的目录
check_directories() {
    # 检查配置目录
    if [ ! -d "config" ]; then
        print_error "配置目录不存在，请确保在项目根目录运行此脚本"
        exit 1
    fi

    # 检查模板目录
    if [ ! -d "templates" ]; then
        print_error "模板目录不存在，请确保在项目根目录运行此脚本"
        exit 1
    fi

    # 检查数据目录
    if [ ! -d "data" ]; then
        print_warning "数据目录不存在，将创建数据目录"
        mkdir -p data/source
        mkdir -p data/processed
        mkdir -p data/open-ended
        mkdir -p data/distill
        mkdir -p data/verified
        mkdir -p data/evaluated
    else
        # 确保所有数据子目录存在
        [ ! -d "data/source" ] && mkdir -p data/source
        [ ! -d "data/processed" ] && mkdir -p data/processed
        [ ! -d "data/sampled" ] && mkdir -p data/sampled
        [ ! -d "data/open-ended" ] && mkdir -p data/open-ended
        [ ! -d "data/distill" ] && mkdir -p data/distill
        [ ! -d "data/verified" ] && mkdir -p data/verified
        [ ! -d "data/evaluated" ] && mkdir -p data/evaluated
    fi

    print_info "目录检查通过"
}

# 设置Python路径
setup_python_path() {
    # 获取当前脚本所在目录的绝对路径
    SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

    # 将项目根目录添加到PYTHONPATH
    export PYTHONPATH="$SCRIPT_DIR:$PYTHONPATH"

    print_info "Python路径设置为: $PYTHONPATH"
}

# 执行预处理流程
run_preprocessing() {
    print_title "执行预处理流程"
    print_info "从data/source读取数据，处理后保存到data/processed"

    python -m preprocess.normalize_datasets --config config/preprocess_config.yaml

    if [ $? -ne 0 ]; then
        print_error "预处理流程执行失败"
        return 1
    fi

    print_info "预处理流程执行完成"
    return 0
}

# 执行抽样流程
run_sampling() {
    print_title "执行抽样流程"
    print_info "从data/processed读取数据，处理后保存到data/sampled"

    python -m pipelines.sampling --config config/sampling_config.yaml

    if [ $? -ne 0 ]; then
        print_error "抽样流程执行失败"
        return 1
    fi

    print_info "抽样流程执行完成"
    return 0
}

# 执行多选题转开放式问题流程
run_conversion() {
    print_title "执行多选题转开放式问题流程"
    print_info "从data/processed读取数据，处理后保存到data/open-ended"

    python -m pipelines.conversion --config config/conversion_config.yaml

    if [ $? -ne 0 ]; then
        print_error "多选题转开放式问题流程执行失败"
        return 1
    fi

    print_info "多选题转开放式问题流程执行完成"
    return 0
}

# 执行蒸馏流程
run_distillation() {
    print_title "执行蒸馏流程"
    print_info "从data/processed读取数据，处理后保存到data/distill"

    python -m pipelines.distillation --config config/distillation_config.yaml

    if [ $? -ne 0 ]; then
        print_error "蒸馏流程执行失败"
        return 1
    fi

    print_info "蒸馏流程执行完成"
    return 0
}

# 执行验证流程
run_verification() {
    print_title "执行验证流程"
    print_info "从data/distill读取数据，处理后保存到data/verified"

    python -m pipelines.verification --config config/verification_config.yaml

    if [ $? -ne 0 ]; then
        print_error "验证流程执行失败"
        return 1
    fi

    print_info "验证流程执行完成"
    return 0
}

# 执行评价流程
run_evaluation() {
    print_title "执行评价流程"
    print_info "从data/verified读取数据，处理后保存到data/evaluated"

    python -m pipelines.evaluation --config config/evaluation_config.yaml

    if [ $? -ne 0 ]; then
        print_error "评价流程执行失败"
        return 1
    fi

    print_info "评价流程执行完成"
    return 0
}

# 显示菜单
show_menu() {
    clear
    print_title "金融数据处理流程"
    echo -e "${CYAN}请选择要执行的流程：${NC}"
    echo -e "${CYAN}1. 执行预处理流程 (data/source -> data/processed)${NC}"
    echo -e "${CYAN}2. 执行抽样流程 (data/processed -> data/sampled)${NC}"
    echo -e "${CYAN}3. 执行多选题转开放式问题流程 (data/processed -> data/open-ended)${NC}"
    echo -e "${CYAN}4. 执行蒸馏流程 (data/processed -> data/distill)${NC}"
    echo -e "${CYAN}5. 执行验证流程 (data/distill -> data/verified)${NC}"
    echo -e "${CYAN}6. 执行评价流程 (data/verified -> data/evaluated)${NC}"
    echo -e "${CYAN}7. 按顺序执行全部流程${NC}"
    echo -e "${CYAN}0. 退出${NC}"
    echo -e "${CYAN}请输入选项 [0-7]: ${NC}"
}

# 主函数
main() {
    # 检查环境
    check_python
    check_directories
    setup_python_path

    # 显示菜单并获取用户选择
    show_menu
    read -r choice

    case $choice in
        1)
            run_preprocessing
            ;;
        2)
            run_sampling
            ;;
        3)
            run_conversion
            ;;
        4)
            run_distillation
            ;;
        5)
            run_verification
            ;;
        6)
            run_evaluation
            ;;
        7)
            print_title "按顺序执行全部流程"

            # 执行预处理流程
            run_preprocessing
            if [ $? -ne 0 ]; then
                print_error "由于预处理流程失败，终止后续流程"
                exit 1
            fi

            # 执行抽样流程
            run_sampling
            if [ $? -ne 0 ]; then
                print_error "由于抽样流程失败，终止后续流程"
                exit 1
            fi

            # 执行多选题转开放式问题流程
            run_conversion
            if [ $? -ne 0 ]; then
                print_error "由于多选题转开放式问题流程失败，终止后续流程"
                exit 1
            fi

            # 执行蒸馏流程
            run_distillation
            if [ $? -ne 0 ]; then
                print_error "由于蒸馏流程失败，终止后续流程"
                exit 1
            fi

            # 执行验证流程
            run_verification
            if [ $? -ne 0 ]; then
                print_error "由于验证流程失败，终止后续流程"
                exit 1
            fi

            # 执行评价流程
            run_evaluation
            if [ $? -ne 0 ]; then
                print_error "评价流程失败"
                exit 1
            fi

            print_info "所有流程执行完成"
            ;;
        0)
            print_info "退出程序"
            exit 0
            ;;
        *)
            print_error "无效的选项，请重新运行脚本并选择有效的选项 [0-7]"
            exit 1
            ;;
    esac
}

# 执行主函数
main
