#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
多选题转开放式问题脚本

该脚本用于将多选题转换为开放式问题，使用synthesis_engine作为核心的合成引擎。
主要功能：
- 加载data/processed目录下的所有数据集
- 提供用户选择需要执行转换的数据集
- 筛选meta.task_type为"mcq"的样本
- 使用mcq_to_open_ended_prompt.txt模板构建prompt
- 从模型响应中提取开放式问题和标准答案
- 将转换结果保存到data/processed目录下，使用原文件名+后缀"_oe"

使用方法：
    python pipelines/conversion.py [--config CONFIG_PATH]
"""

import os
import json
import re
import glob
import yaml
import sys
from typing import Dict, Any, Tuple
import argparse
from pathlib import Path

# 导入合成引擎
from tools.synthesis_engine import run_synthesis


def extract_conversion_result(response: str) -> <PERSON><PERSON>[str, str]:
    """
    从模型响应中提取开放式问题和标准答案

    Args:
        response: 模型响应文本

    Returns:
        Tuple[str, str]: (question, answer)
    """

    try:
        # 尝试解析JSON格式的响应
        json_match = re.search(r'({.*})', response, re.DOTALL)
        if json_match:
            json_str = json_match.group(1)
            data = json.loads(json_str)
            question = data.get("question", "")
            answer = data.get("answer", "")
            return question, answer
    except Exception as e:
        print(f"解析JSON失败: {str(e)}")

    # 如果JSON解析失败，尝试使用正则表达式提取
    question_match = re.search(r'"question":\s*"([^"]*)"', response)
    answer_match = re.search(r'"answer":\s*"([^"]*)"', response)

    question = question_match.group(1) if question_match else ""
    answer = answer_match.group(1) if answer_match else ""

    return question, answer


def load_config(config_path: str = "config/conversion_config.yaml") -> Dict[str, Any]:
    """
    加载配置文件

    Args:
        config_path: 配置文件路径

    Returns:
        Dict[str, Any]: 配置信息
    """
    try:
        # 确保配置文件路径是绝对路径
        if not os.path.isabs(config_path):
            config_path = os.path.abspath(config_path)

        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        print(f"加载配置文件失败: {str(e)}")
        print(f"尝试加载的路径: {config_path}")
        return {}


def load_prompt_template(template_path: str) -> str:
    """
    加载提示词模板

    Args:
        template_path: 模板文件路径

    Returns:
        str: 提示词模板

    Raises:
        Exception: 如果模板加载失败
    """
    # 确保模板路径是绝对路径
    if not os.path.isabs(template_path):
        template_path = os.path.abspath(template_path)

    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            template = f.read()
        return template
    except Exception as e:
        print(f"错误: 加载提示词模板失败: {str(e)}")
        print(f"尝试加载的路径: {template_path}")
        print(f"请确保模板文件存在并且可读")
        raise


def construct_prompt(data: Dict[str, Any], prompt_template: str, prompt_template_zh: str = None) -> Tuple[str, str]:
    """
    构造转换提示词

    Args:
        data: 原始数据
        prompt_template: 提示词模板（英文）
        prompt_template_zh: 提示词模板（中文），如果为None则使用英文模板

    Returns:
        Tuple[str, str]: (instruction, prompt)
    """

    # 检查是否是多选题
    if data.get("meta", {}).get("task_type", "") != "mcq":
        return "", ""

    # 根据meta中的language字段选择模板
    current_template = prompt_template
    if prompt_template_zh is not None:
        language = data.get("meta", {}).get("language", "")
        if language.lower() == "zh":
            current_template = prompt_template_zh
            print(f"使用中文模板处理样本 ID: {data.get('id', 'unknown')}")
        else:
            print(f"使用英文模板处理样本 ID: {data.get('id', 'unknown')}")

    # 从conversations中提取问题和正确答案
    question = ""
    options = ""
    correct_answer = ""

    for conv in data.get("conversations", []):
        if conv.get("role") == "user":
            # 获取问题和选项
            user_content = conv.get("content", "")
            # 尝试分离问题和选项
            lines = user_content.split('\n')
            if len(lines) > 1:
                question = lines[0]
                options = '\n'.join(lines[1:])
            else:
                question = user_content
        elif conv.get("role") == "assistant":
            # 获取正确答案
            correct_answer = conv.get("final_answer", "")

    # 构造提示词 - 使用选择的模板
    # 使用替换而不是format，以避免模板中的大括号导致的问题
    prompt = current_template
    prompt = prompt.replace("{question}", question)
    prompt = prompt.replace("{options}", options)
    prompt = prompt.replace("{correct_answer}", correct_answer)

    return "将多选题转换为开放式问题", prompt


def process_response(task: Dict[str, Any], response: str, api_reasoning: str = "") -> Dict[str, Any]:
    """
    处理模型响应

    Args:
        task: 任务信息
        response: 模型响应
        api_reasoning: API返回的reasoning字段内容（如果有）

    Returns:
        Dict[str, Any]: 处理后的结果
    """
    # 提取开放式问题和标准答案
    question, answer = extract_conversion_result(response)

    # 复制原始数据
    result = task["raw_data"].copy()

    # 创建新的conversations
    new_conversations = [{"role": "system", "content": ""}]

    # 添加新的user消息（开放式问题）
    new_conversations.append({
        "role": "user",
        "content": question
    })

    # 添加新的assistant消息（标准答案）
    new_conversations.append({
        "role": "assistant",
        "content": answer,
        "reasoning": "",
        "final_answer": answer
    })

    # 更新conversations
    result["conversations"] = new_conversations

    # 更新meta信息
    result["meta"]["task_desc"] = "开放式问答"
    result["meta"]["task_type"] = "open_ended"

    return result


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="多选题转开放式问题脚本")
    parser.add_argument("--config", type=str, default="config/conversion_config.yaml",
                        help="配置文件路径 (默认: config/conversion_config.yaml)")
    args = parser.parse_args()

    # 确保配置文件路径是绝对路径
    if not os.path.isabs(args.config):
        # 如果是相对路径，则相对于项目根目录
        args.config = os.path.abspath(args.config)

    return args


def check_api_config(config: Dict[str, Any]) -> bool:
    """
    检查API配置是否完整

    Args:
        config: 配置信息

    Returns:
        bool: 配置是否有效
    """
    if not config.get("API_KEY"):
        print(f"错误: 配置文件中未设置API密钥")
        print(f"请在配置文件中设置有效的API_KEY值")
        return False

    if not config.get("API_URL"):
        print(f"错误: 配置文件中未设置API_URL")
        print(f"请在配置文件中设置有效的API_URL值")
        return False

    if not config.get("MODEL"):
        print(f"错误: 配置文件中未设置MODEL")
        print(f"请在配置文件中设置有效的MODEL值")
        return False

    return True


def load_templates(config: Dict[str, Any]) -> Tuple[str, str]:
    """
    加载提示词模板（英文和中文）

    Args:
        config: 配置信息

    Returns:
        Tuple[str, str]: (英文模板, 中文模板)
    """
    prompt_template_path = config.get("PROMPT_TEMPLATE_PATH", "templates/mcq_to_open_ended_prompt.txt")
    prompt_template_zh_path = prompt_template_path.replace('.txt', '_zh.txt')

    try:
        # 加载英文模板
        prompt_template = load_prompt_template(prompt_template_path)

        # 尝试加载中文模板
        try:
            prompt_template_zh = load_prompt_template(prompt_template_zh_path)
            print(f"成功加载中文模板: {prompt_template_zh_path}")
        except Exception as e:
            print(f"警告: 无法加载中文提示词模板: {prompt_template_zh_path}，将对中文样本使用英文模板")
            prompt_template_zh = prompt_template

        return prompt_template, prompt_template_zh
    except Exception as e:
        print(f"错误: 无法加载提示词模板: {prompt_template_path}")
        print(f"请确保模板文件存在并且可读")
        raise


def find_datasets(input_dir: str) -> list:
    """
    查找所有数据集

    Args:
        input_dir: 输入目录

    Returns:
        list: 数据集路径列表
    """
    datasets = []
    for jsonl_file in glob.glob(f"{input_dir}/**/*.jsonl", recursive=True):
        datasets.append(jsonl_file)
    return sorted(datasets)


def select_datasets(datasets: list) -> list:
    """
    用户选择要处理的数据集

    Args:
        datasets: 所有数据集列表

    Returns:
        list: 选中的数据集列表
    """
    print("找到以下数据集：")
    for i, dataset in enumerate(datasets):
        print(f"[{i}] {dataset}")

    choice = input("\n请输入要转换的数据集序号（多个序号用逗号分隔，输入'all'处理所有数据集）: ")

    if choice.lower() == 'all':
        selected_datasets = datasets
    else:
        indices = [int(idx.strip()) for idx in choice.split(',')]
        selected_datasets = [datasets[idx] for idx in indices if 0 <= idx < len(datasets)]

    if not selected_datasets:
        print("未选择任何数据集，退出程序")
        return []

    print(f"\n将处理以下数据集：")
    for dataset in selected_datasets:
        print(f"- {dataset}")

    return selected_datasets


def get_engine_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    获取合成引擎配置

    Args:
        config: 主配置

    Returns:
        Dict[str, Any]: 引擎配置
    """
    return {
        "API_KEY": config["API_KEY"],
        "API_URL": config["API_URL"],
        "MODEL": config["MODEL"],
        "NUM_PROCESSORS": config.get("NUM_PROCESSORS", 4),
        "MAX_RETRIES": config.get("MAX_RETRIES", 3),
        "RATE_LIMIT_WAIT": config.get("RATE_LIMIT_WAIT", 10),
        "MAX_QUEUE_SIZE": config.get("MAX_QUEUE_SIZE", 100),
        "API_PARAMS": config.get("API_PARAMS", {
            "temperature": 0.7,
            "max_tokens": 2048
        })
    }


def process_dataset(dataset: str, input_dir: str, output_dir: str,
                   prompt_template: str, prompt_template_zh: str,
                   engine_config: Dict[str, Any]) -> str:
    """
    处理单个数据集

    Args:
        dataset: 数据集路径
        input_dir: 输入目录
        output_dir: 输出目录
        prompt_template: 英文提示词模板
        prompt_template_zh: 中文提示词模板
        engine_config: 引擎配置

    Returns:
        str: 输出文件路径
    """
    print(f"\n开始处理数据集: {dataset}")

    # 构建输出路径 - 保持与原始数据集相同的子路径结构
    rel_path = os.path.relpath(dataset, input_dir)
    output_path = os.path.join(output_dir, rel_path)
    output_dir_for_dataset = os.path.dirname(output_path)

    # 创建输出目录
    os.makedirs(output_dir_for_dataset, exist_ok=True)

    # 创建构造提示词的函数，传递英文和中文模板
    def construct_prompt_with_template(data):
        return construct_prompt(data, prompt_template, prompt_template_zh)

    # 运行合成引擎
    run_synthesis(
        input_dir=os.path.dirname(dataset),
        output_dir=output_dir_for_dataset,
        construct_prompt_func=construct_prompt_with_template,
        process_response_func=process_response,
        config=engine_config
    )

    return output_path


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()

    # 加载配置
    config = load_config(args.config)
    if not config:
        print(f"无法加载配置文件: {args.config}")
        # 尝试加载distillation配置作为备选
        config = load_config("config/distillation_config.yaml")
        if not config:
            print("无法加载备选配置文件，使用默认配置")
            config = {}

    # 检查必要的API配置
    if not check_api_config(config):
        return

    try:
        # 加载提示词模板
        prompt_template, prompt_template_zh = load_templates(config)

        # 创建输出目录
        output_dir = config.get("OUTPUT_DIR", "data/processed")
        os.makedirs(output_dir, exist_ok=True)

        # 查找所有数据集
        input_dir = config.get("INPUT_DIR", "data/processed")
        datasets = find_datasets(input_dir)

        if not datasets:
            print(f"未找到任何数据集，请确保{input_dir}目录下有.jsonl文件")
            return

        # 用户选择数据集
        selected_datasets = select_datasets(datasets)
        if not selected_datasets:
            return

        # 获取引擎配置
        engine_config = get_engine_config(config)

        # 处理每个数据集
        for dataset in selected_datasets:
            # 处理数据集
            output_path = process_dataset(
                dataset, input_dir, output_dir,
                prompt_template, prompt_template_zh,
                engine_config
            )

            print(f"数据集 {dataset} 处理完成")
            print(f"转换结果保存在: {output_path}")

        print("\n所有数据集处理完成")

    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"\n发生错误: {str(e)}")


if __name__ == "__main__":
    main()
