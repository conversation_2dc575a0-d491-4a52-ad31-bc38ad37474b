#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据蒸馏脚本

该脚本用于对数据集进行蒸馏处理，使用synthesis_engine作为核心的合成引擎。
主要功能：
- 加载data/processed目录下的所有数据集
- 提供用户选择需要执行蒸馏的数据集
- 使用指定的prompt模板进行蒸馏
- 从模型响应中抽取reasoning、content和final_answer
- 将蒸馏结果保存到data/distill目录下

使用方法：
    python distil/distillation.py [--config CONFIG_PATH]
"""

import os
import json
import re
import glob
import yaml
import sys
from typing import Dict, Any, Tuple
import argparse
from pathlib import Path

# 导入合成引擎
from tools.synthesis_engine import run_synthesis


def extract_from_response(response: str) -> Tuple[str, str, str]:
    """
    从模型响应中提取reasoning、content和final_answer

    Args:
        response: 模型响应文本

    Returns:
        <PERSON><PERSON>[str, str, str]: (reasoning, content, final_answer)
    """

    # 提取<think></think>标签中的内容作为reasoning
    reasoning = ""
    think_match = re.search(r'<think>(.*?)</think>', response, re.DOTALL)
    if think_match:
        reasoning = think_match.group(1).strip()
        # 从response中移除<think></think>部分
        response = re.sub(r'<think>.*?</think>', '', response, flags=re.DOTALL)

    # 提取\boxed{}中的内容作为final_answer
    final_answer = ""
    boxed_match = re.search(r'\\boxed{(.*?)}', response, re.DOTALL)
    if boxed_match:
        final_answer = boxed_match.group(1).strip()

    # 剩余部分作为content（不包括<think></think>标签）
    content = response.strip()

    return reasoning, content, final_answer

def load_config(config_path: str = "config/distillation_config.yaml") -> Dict[str, Any]:
    """
    加载配置文件

    Args:
        config_path: 配置文件路径

    Returns:
        Dict[str, Any]: 配置信息
    """
    try:
        # 确保配置文件路径是绝对路径
        if not os.path.isabs(config_path):
            config_path = os.path.abspath(config_path)

        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        print(f"加载配置文件失败: {str(e)}")
        print(f"尝试加载的路径: {config_path}")
        return {}

def load_prompt_template(template_path: str) -> str:
    """
    加载提示词模板

    Args:
        template_path: 模板文件路径

    Returns:
        str: 提示词模板

    Raises:
        Exception: 如果模板加载失败
    """
    # 确保模板路径是绝对路径
    if not os.path.isabs(template_path):
        template_path = os.path.abspath(template_path)

    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            template = f.read()
        return template
    except Exception as e:
        print(f"错误: 加载提示词模板失败: {str(e)}")
        print(f"尝试加载的路径: {template_path}")
        print(f"请确保模板文件存在并且可读")
        raise

def construct_prompt(data: Dict[str, Any], prompt_template: str, prompt_template_zh: str = None) -> Tuple[str, str]:
    """
    构造蒸馏提示词

    Args:
        data: 原始数据
        prompt_template: 提示词模板（英文）
        prompt_template_zh: 提示词模板（中文），如果为None则使用英文模板

    Returns:
        Tuple[str, str]: (instruction, prompt)
    """
    # 根据meta中的language字段选择模板
    current_template = prompt_template
    if prompt_template_zh is not None:
        language = data.get("meta", {}).get("language", "")
        if language.lower() == "zh":
            current_template = prompt_template_zh
            print(f"使用中文模板处理样本 ID: {data.get('id', 'unknown')}")
        else:
            print(f"使用英文模板处理样本 ID: {data.get('id', 'unknown')}")
    # 获取meta中的task_desc
    task_desc = data.get("meta", {}).get("task_desc", "")

    # 从conversations中提取system内容和所有对话
    system_content = ""
    conversations = data.get("conversations", [])

    # 提取system内容
    for conv in conversations:
        if conv.get("role") == "system":
            system_content = conv.get("content", "")
            break

    # 处理多轮对话
    user_assistant_pairs = []
    last_user_content = ""

    # 遍历所有对话，收集user-assistant对
    i = 0
    while i < len(conversations):
        conv = conversations[i]
        role = conv.get("role", "")

        # 跳过system角色
        if role == "system":
            i += 1
            continue

        # 如果是user角色
        if role == "user":
            user_msg = conv.get("content", "")
            assistant_msg = ""

            # 查找下一个assistant消息
            if i + 1 < len(conversations) and conversations[i + 1].get("role") == "assistant":
                assistant_msg = conversations[i + 1].get("content", "")
                # 收集完整的对话对
                if i + 2 < len(conversations) and conversations[i + 2].get("role") == "user":
                    user_assistant_pairs.append((user_msg, assistant_msg))
                else:
                    # 最后一对，记录最后的user内容
                    last_user_content = user_msg
                i += 2  # 跳过已处理的assistant消息
            else:
                # 没有对应的assistant消息，这是最后一个user消息
                last_user_content = user_msg
                i += 1
        else:
            # 其他角色，直接跳过
            i += 1

    # 如果没有找到最后的user内容，但有user-assistant对，使用最后一对的user内容
    if not last_user_content and user_assistant_pairs:
        last_user_content = user_assistant_pairs[-1][0]
        user_assistant_pairs.pop()

    # 如果仍然没有user内容，尝试从conversations中找到最后一个user消息
    if not last_user_content:
        for conv in reversed(conversations):
            if conv.get("role") == "user":
                last_user_content = conv.get("content", "")
                break

    # 获取documents字段内容
    documents = data.get("documents", [])
    document_text = "\n\n".join(documents) if documents else ""

    # 构建历史对话文本
    history_text = ""
    if user_assistant_pairs:
        history_parts = []
        for user_msg, assistant_msg in user_assistant_pairs:
            history_parts.append(f"User: {user_msg}\nAssistant: {assistant_msg}")
        history_text = "\n\n".join(history_parts)

    # 确定task_instruction和task_input
    task_instruction = ""
    task_input = ""

    if system_content:
        # 如果有system_content，将其作为task_instruction
        task_instruction = system_content

        # 如果有documents且不为空，将其添加到task_instruction
        if document_text:
            task_instruction = f"{task_instruction}\n\nDocuments: \n{document_text}"

        # 如果有历史对话，添加到task_instruction
        if history_text:
            task_instruction = f"{task_instruction}\n\nPrevious conversation:\n{history_text}"

        # 最后一轮的user_content作为task_input
        task_input = last_user_content
    else:
        # 如果没有system_content

        # 如果没有历史对话，则task_input为空，user_content作为task_instruction
        task_instruction = last_user_content

        # 如果有documents且不为空，将其添加到task_input
        if document_text:
            task_input = document_text

       # 如果有历史对话，添加到task_instruction
        if history_text:
            if task_input:
                task_input += f"\n\nPrevious conversation:\n{history_text}"
            else:
                task_input = f"Previous conversation:\n{history_text}"

    # 构造提示词 - 使用选择的模板
    prompt = current_template.format(
        task_desc=task_desc,
        task_input=task_input,
        task_instruction=task_instruction
    )

    return task_instruction, prompt

def process_response(task: Dict[str, Any], response: str, api_reasoning: str = "") -> Dict[str, Any]:
    """
    处理模型响应

    Args:
        task: 任务信息
        response: 模型响应
        api_reasoning: API返回的reasoning字段内容（如果有）

    Returns:
        Dict[str, Any]: 处理后的结果
    """
    # 如果API返回了reasoning字段，直接使用它
    if api_reasoning:
        reasoning = api_reasoning
        content = response
        # 从response中提取final_answer
        final_answer = ""
        boxed_match = re.search(r'\\boxed{(.*?)}', response, re.DOTALL)
        if boxed_match:
            final_answer = boxed_match.group(1).strip()
    else:
        # 否则，从response中提取reasoning、content和final_answer
        reasoning, content, final_answer = extract_from_response(response)

    # 复制原始数据
    result = task["raw_data"].copy()

    # 找到最后一个assistant角色的消息
    last_assistant_idx = -1
    for i, conv in enumerate(result.get("conversations", [])):
        if conv.get("role") == "assistant":
            last_assistant_idx = i

    # 如果找到了assistant角色的消息，添加蒸馏结果
    if last_assistant_idx >= 0:
        # 添加蒸馏结果到最后一个assistant消息
        if reasoning:
            result["conversations"][last_assistant_idx]["distill_reasoning"] = reasoning
        result["conversations"][last_assistant_idx]["distill_content"] = content
        result["conversations"][last_assistant_idx]["distill_final_answer"] = final_answer

    return result

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="数据蒸馏脚本")
    parser.add_argument("--config", type=str, default="config/distillation_config.yaml",
                        help="配置文件路径 (默认: config/distillation_config.yaml)")
    args = parser.parse_args()

    # 确保配置文件路径是绝对路径
    if not os.path.isabs(args.config):
        # 如果是相对路径，则相对于项目根目录
        args.config = os.path.abspath(args.config)

    return args

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()

    # 加载配置
    config = load_config(args.config)
    if not config:
        print(f"无法加载配置文件: {args.config}")
        return

    # 检查必要的API配置
    if not config.get("API_KEY"):
        print(f"错误: 配置文件 {args.config} 中未设置API密钥")
        print(f"请在配置文件中设置有效的API_KEY值")
        return

    if not config.get("API_URL"):
        print(f"错误: 配置文件 {args.config} 中未设置API_URL")
        print(f"请在配置文件中设置有效的API_URL值")
        return

    if not config.get("MODEL"):
        print(f"错误: 配置文件 {args.config} 中未设置MODEL")
        print(f"请在配置文件中设置有效的MODEL值")
        return

    # 加载提示词模板（英文和中文）
    prompt_template_path = config.get("PROMPT_TEMPLATE_PATH", "templates/distillation_prompt.txt")
    prompt_template_zh_path = prompt_template_path.replace('.txt', '_zh.txt')

    try:
        # 加载英文模板
        prompt_template = load_prompt_template(prompt_template_path)

        # 尝试加载中文模板
        try:
            prompt_template_zh = load_prompt_template(prompt_template_zh_path)
            print(f"成功加载中文模板: {prompt_template_zh_path}")
        except Exception as e:
            print(f"警告: 无法加载中文提示词模板: {prompt_template_zh_path}，将对中文样本使用英文模板")
            prompt_template_zh = prompt_template
    except Exception as e:
        print(f"错误: 无法加载提示词模板: {prompt_template_path}")
        print(f"请确保模板文件存在并且可读")
        return

    # 创建输出目录
    output_dir = config.get("OUTPUT_DIR", "data/distill")
    os.makedirs(output_dir, exist_ok=True)

    # 查找所有数据集
    input_dir = config.get("INPUT_DIR", "data/processed")
    datasets = []
    for jsonl_file in glob.glob(f"{input_dir}/**/*.jsonl", recursive=True):
        datasets.append(jsonl_file)
    datasets = sorted(datasets)

    if not datasets:
        print(f"未找到任何数据集，请确保{input_dir}目录下有.jsonl文件")
        return

    # 显示数据集列表
    print("找到以下数据集：")
    for i, dataset in enumerate(datasets):
        print(f"[{i}] {dataset}")

    # 用户选择数据集
    try:
        choice = input("\n请输入要蒸馏的数据集序号（多个序号用逗号分隔，输入'all'处理所有数据集）: ")

        if choice.lower() == 'all':
            selected_datasets = datasets
        else:
            indices = [int(idx.strip()) for idx in choice.split(',')]
            selected_datasets = [datasets[idx] for idx in indices if 0 <= idx < len(datasets)]

        if not selected_datasets:
            print("未选择任何数据集，退出程序")
            return

        print(f"\n将处理以下数据集：")
        for dataset in selected_datasets:
            print(f"- {dataset}")

        # 处理每个数据集
        for dataset in selected_datasets:
            print(f"\n开始处理数据集: {dataset}")

            # 构建输出路径 - 保持与原始数据集相同的子路径结构
            rel_path = os.path.relpath(dataset, input_dir)
            output_path = os.path.join(output_dir, rel_path)
            output_dir_for_dataset = os.path.dirname(output_path)

            # 创建输出目录
            os.makedirs(output_dir_for_dataset, exist_ok=True)

            # 直接使用配置文件中的API配置
            engine_config = {
                "API_KEY": config["API_KEY"],
                "API_URL": config["API_URL"],
                "MODEL": config["MODEL"],
                "NUM_PROCESSORS": config.get("NUM_PROCESSORS", 4),
                "MAX_RETRIES": config.get("MAX_RETRIES", 3),
                "RATE_LIMIT_WAIT": config.get("RATE_LIMIT_WAIT", 10),
                "MAX_QUEUE_SIZE": config.get("MAX_QUEUE_SIZE", 100),
                "API_PARAMS": config.get("API_PARAMS", {
                    "temperature": 0.7,
                    "max_tokens": 2048
                })
            }

            # 创建构造提示词的函数，传递英文和中文模板
            def construct_prompt_with_template(data):
                return construct_prompt(data, prompt_template, prompt_template_zh)

            # 运行合成引擎
            run_synthesis(
                input_dir=os.path.dirname(dataset),
                output_dir=output_dir_for_dataset,
                construct_prompt_func=construct_prompt_with_template,
                process_response_func=process_response,
                config=engine_config
            )

            print(f"数据集 {dataset} 处理完成，结果保存在 {output_path}")

        print("\n所有数据集处理完成")

    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"\n发生错误: {str(e)}")

if __name__ == "__main__":
    main()