#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据验证脚本

该脚本用于对蒸馏后的数据集进行验证处理，使用synthesis_engine作为核心的合成引擎。
主要功能：
- 加载data/distill目录下的所有数据集
- 提供用户选择需要执行验证的数据集
- 使用judging_prompt.txt模板进行验证
- 验证蒸馏后的答案是否正确
- 将验证结果分为正确和不正确两个文件，保存到data/verified目录下

使用方法：
    python pipelines/verification.py [--config CONFIG_PATH]
"""

import os
import json
import re
import glob
import yaml
import sys
from typing import Dict, Any, Tuple
import argparse
from pathlib import Path

# 导入合成引擎
from tools.synthesis_engine import run_synthesis


def extract_verification_result(response: str) -> int:
    """
    从模型响应中提取验证结果（1表示正确，0表示不正确）

    Args:
        response: 模型响应文本

    Returns:
        int: 验证结果（1或0）
    """
    # 提取\boxed{}中的内容作为验证结果
    boxed_match = re.search(r'\\boxed{(\d)}', response, re.DOTALL)
    if boxed_match:
        result = int(boxed_match.group(1).strip())
        return result
    return -1  # 返回-1表示未找到有效结果


def load_config(config_path: str = "config/verification_config.yaml") -> Dict[str, Any]:
    """
    加载配置文件

    Args:
        config_path: 配置文件路径

    Returns:
        Dict[str, Any]: 配置信息
    """
    try:
        # 确保配置文件路径是绝对路径
        if not os.path.isabs(config_path):
            config_path = os.path.abspath(config_path)

        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        print(f"加载配置文件失败: {str(e)}")
        print(f"尝试加载的路径: {config_path}")
        return {}


def load_prompt_template(template_path: str) -> str:
    """
    加载提示词模板

    Args:
        template_path: 模板文件路径

    Returns:
        str: 提示词模板

    Raises:
        Exception: 如果模板加载失败
    """
    # 确保模板路径是绝对路径
    if not os.path.isabs(template_path):
        template_path = os.path.abspath(template_path)

    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            template = f.read()
        return template
    except Exception as e:
        print(f"错误: 加载提示词模板失败: {str(e)}")
        print(f"尝试加载的路径: {template_path}")
        print(f"请确保模板文件存在并且可读")
        raise


def construct_prompt(data: Dict[str, Any], prompt_template: str, prompt_template_zh: str = None) -> Tuple[str, str]:
    """
    构造验证提示词

    Args:
        data: 原始数据
        prompt_template: 提示词模板（英文）
        prompt_template_zh: 提示词模板（中文），如果为None则使用英文模板

    Returns:
        Tuple[str, str]: (instruction, prompt)
    """
    # 根据meta中的language字段选择模板
    current_template = prompt_template
    if prompt_template_zh is not None:
        language = data.get("meta", {}).get("language", "")
        if language.lower() == "zh":
            current_template = prompt_template_zh
            print(f"使用中文模板处理样本 ID: {data.get('id', 'unknown')}")
        else:
            print(f"使用英文模板处理样本 ID: {data.get('id', 'unknown')}")
    # 从conversations中提取标准答案和蒸馏答案
    ground_truth = ""
    distill_answer = ""

    for conv in data.get("conversations", []):
        if conv.get("role") == "assistant":
            # 获取标准答案
            ground_truth = conv.get("final_answer", "")
            # 获取蒸馏答案
            distill_answer = conv.get("distill_final_answer", "")
            break

    # 构造提示词 - 使用选择的模板
    prompt = current_template.format(
        Truth=ground_truth,
        Answer=distill_answer
    )
    print(prompt)
    return "验证蒸馏答案是否与标准答案一致", prompt


def process_response(task: Dict[str, Any], response: str, api_reasoning: str = "") -> Dict[str, Any]:
    """
    处理模型响应

    Args:
        task: 任务信息
        response: 模型响应
        api_reasoning: API返回的reasoning字段内容（如果有）

    Returns:
        Dict[str, Any]: 处理后的结果
    """
    # 提取验证结果
    verification_result = extract_verification_result(response)

    # 复制原始数据
    result = task["raw_data"].copy()

    # 添加验证结果
    result["verification_result"] = verification_result
    result["verification_reasoning"] = api_reasoning
    result["verification_response"] = response

    # 添加验证状态标记
    if verification_result == 1:
        result["verification_status"] = "correct"
    elif verification_result == 0:
        result["verification_status"] = "incorrect"
    else:
        result["verification_status"] = "unknown"

    return result


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="数据验证脚本")
    parser.add_argument("--config", type=str, default="config/verification_config.yaml",
                        help="配置文件路径 (默认: config/verification_config.yaml)")
    args = parser.parse_args()

    # 确保配置文件路径是绝对路径
    if not os.path.isabs(args.config):
        # 如果是相对路径，则相对于项目根目录
        args.config = os.path.abspath(args.config)

    return args


def check_api_config(config: Dict[str, Any]) -> bool:
    """
    检查API配置是否完整

    Args:
        config: 配置信息

    Returns:
        bool: 配置是否有效
    """
    if not config.get("API_KEY"):
        print(f"错误: 配置文件中未设置API密钥")
        print(f"请在配置文件中设置有效的API_KEY值")
        return False

    if not config.get("API_URL"):
        print(f"错误: 配置文件中未设置API_URL")
        print(f"请在配置文件中设置有效的API_URL值")
        return False

    if not config.get("MODEL"):
        print(f"错误: 配置文件中未设置MODEL")
        print(f"请在配置文件中设置有效的MODEL值")
        return False

    return True


def load_templates(config: Dict[str, Any]) -> Tuple[str, str]:
    """
    加载提示词模板（英文和中文）

    Args:
        config: 配置信息

    Returns:
        Tuple[str, str]: (英文模板, 中文模板)
    """
    prompt_template_path = config.get("PROMPT_TEMPLATE_PATH", "templates/judging_prompt.txt")
    prompt_template_zh_path = prompt_template_path.replace('.txt', '_zh.txt')

    try:
        # 加载英文模板
        prompt_template = load_prompt_template(prompt_template_path)

        # 尝试加载中文模板
        try:
            prompt_template_zh = load_prompt_template(prompt_template_zh_path)
            print(f"成功加载中文模板: {prompt_template_zh_path}")
        except Exception as e:
            print(f"警告: 无法加载中文提示词模板: {prompt_template_zh_path}，将对中文样本使用英文模板")
            prompt_template_zh = prompt_template

        return prompt_template, prompt_template_zh
    except Exception as e:
        print(f"错误: 无法加载提示词模板: {prompt_template_path}")
        print(f"请确保模板文件存在并且可读")
        raise


def find_datasets(input_dir: str) -> list:
    """
    查找所有数据集

    Args:
        input_dir: 输入目录

    Returns:
        list: 数据集路径列表
    """
    datasets = []
    for jsonl_file in glob.glob(f"{input_dir}/**/*.jsonl", recursive=True):
        datasets.append(jsonl_file)
    return sorted(datasets)


def select_datasets(datasets: list) -> list:
    """
    用户选择要处理的数据集

    Args:
        datasets: 所有数据集列表

    Returns:
        list: 选中的数据集列表
    """
    print("找到以下数据集：")
    for i, dataset in enumerate(datasets):
        print(f"[{i}] {dataset}")

    choice = input("\n请输入要验证的数据集序号（多个序号用逗号分隔，输入'all'处理所有数据集）: ")

    if choice.lower() == 'all':
        selected_datasets = datasets
    else:
        indices = [int(idx.strip()) for idx in choice.split(',')]
        selected_datasets = [datasets[idx] for idx in indices if 0 <= idx < len(datasets)]

    if not selected_datasets:
        print("未选择任何数据集，退出程序")
        return []

    print(f"\n将处理以下数据集：")
    for dataset in selected_datasets:
        print(f"- {dataset}")

    return selected_datasets


def get_engine_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    获取合成引擎配置

    Args:
        config: 主配置

    Returns:
        Dict[str, Any]: 引擎配置
    """
    return {
        "API_KEY": config["API_KEY"],
        "API_URL": config["API_URL"],
        "MODEL": config["MODEL"],
        "NUM_PROCESSORS": config.get("NUM_PROCESSORS", 4),
        "MAX_RETRIES": config.get("MAX_RETRIES", 3),
        "RATE_LIMIT_WAIT": config.get("RATE_LIMIT_WAIT", 10),
        "MAX_QUEUE_SIZE": config.get("MAX_QUEUE_SIZE", 100),
        "API_PARAMS": config.get("API_PARAMS", {
            "temperature": 0.2,  # 降低温度以获得更确定的结果
            "max_tokens": 1024
        })
    }


def process_dataset(dataset: str, input_dir: str, output_dir: str,
                   prompt_template: str, prompt_template_zh: str,
                   engine_config: Dict[str, Any]) -> str:
    """
    处理单个数据集

    Args:
        dataset: 数据集路径
        input_dir: 输入目录
        output_dir: 输出目录
        prompt_template: 英文提示词模板
        prompt_template_zh: 中文提示词模板
        engine_config: 引擎配置

    Returns:
        str: 输出文件路径
    """
    print(f"\n开始处理数据集: {dataset}")

    # 构建输出路径 - 保持与原始数据集相同的子路径结构
    rel_path = os.path.relpath(dataset, input_dir)
    output_path = os.path.join(output_dir, rel_path)
    output_dir_for_dataset = os.path.dirname(output_path)

    # 创建输出目录
    os.makedirs(output_dir_for_dataset, exist_ok=True)

    # 创建构造提示词的函数，传递英文和中文模板
    def construct_prompt_with_template(data):
        return construct_prompt(data, prompt_template, prompt_template_zh)

    # 运行合成引擎
    run_synthesis(
        input_dir=os.path.dirname(dataset),
        output_dir=output_dir_for_dataset,
        construct_prompt_func=construct_prompt_with_template,
        process_response_func=process_response,
        config=engine_config
    )

    return output_path


def split_verification_results(output_path: str, output_dir_for_dataset: str) -> Tuple[str, str]:
    """
    将验证结果分为正确和不正确两个文件，保存到不同文件夹

    Args:
        output_path: 验证结果文件路径
        output_dir_for_dataset: 数据集输出目录

    Returns:
        Tuple[str, str]: (正确结果路径, 不正确结果路径)
    """
    # 创建correct和incorrect子文件夹
    correct_dir = os.path.join(output_dir_for_dataset, "correct")
    incorrect_dir = os.path.join(output_dir_for_dataset, "incorrect")
    os.makedirs(correct_dir, exist_ok=True)
    os.makedirs(incorrect_dir, exist_ok=True)

    # 构建输出文件路径
    base_filename = os.path.basename(output_path)
    correct_path = os.path.join(correct_dir, base_filename)
    incorrect_path = os.path.join(incorrect_dir, base_filename)

    # 打开输出文件
    with open(correct_path, 'w', encoding='utf-8') as f_correct, \
         open(incorrect_path, 'w', encoding='utf-8') as f_incorrect:

        # 读取验证结果
        with open(output_path, 'r', encoding='utf-8') as f_result:
            for line in f_result:
                data = json.loads(line)
                if data.get("verification_status") == "correct":
                    f_correct.write(line)
                elif data.get("verification_status") == "incorrect":
                    f_incorrect.write(line)

    return correct_path, incorrect_path


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()

    # 加载配置
    config = load_config(args.config)
    if not config:
        print(f"无法加载配置文件: {args.config}")
        # 尝试加载distillation配置作为备选
        config = load_config("config/distillation_config.yaml")
        if not config:
            print("无法加载备选配置文件，使用默认配置")
            config = {}

    # 检查必要的API配置
    if not check_api_config(config):
        return

    try:
        # 加载提示词模板
        prompt_template, prompt_template_zh = load_templates(config)

        # 创建输出目录
        output_dir = config.get("OUTPUT_DIR", "data/verified")
        os.makedirs(output_dir, exist_ok=True)

        # 查找所有数据集
        input_dir = config.get("INPUT_DIR", "data/distill")
        datasets = find_datasets(input_dir)

        if not datasets:
            print(f"未找到任何数据集，请确保{input_dir}目录下有.jsonl文件")
            return

        # 用户选择数据集
        selected_datasets = select_datasets(datasets)
        if not selected_datasets:
            return

        # 获取引擎配置
        engine_config = get_engine_config(config)

        # 处理每个数据集
        for dataset in selected_datasets:
            # 处理数据集
            output_path = process_dataset(
                dataset, input_dir, output_dir,
                prompt_template, prompt_template_zh,
                engine_config
            )

            # 拆分验证结果
            output_dir_for_dataset = os.path.dirname(output_path)
            correct_path, incorrect_path = split_verification_results(
                output_path, output_dir_for_dataset
            )

            print(f"数据集 {dataset} 处理完成")
            print(f"正确答案保存在: {correct_path}")
            print(f"不正确答案保存在: {incorrect_path}")

        print("\n所有数据集处理完成")

    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"\n发生错误: {str(e)}")


if __name__ == "__main__":
    main()
