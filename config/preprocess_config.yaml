# 数据预处理配置

# 源数据和目标数据目录
SOURCE_DIR: "data/source"
TARGET_DIR: "data/processed"

# 是否处理特殊数据集
PROCESS_ANT_FINANCE: true
PROCESS_SUFE_FINANCE: true

# 数据集处理器映射
# 这些映射在normalize_datasets.py中使用
# 格式: 路径: 处理函数
PROCESSORS:
  # 精确文件匹配
  "dianjin-r1/cflue_mcq.json": "process_dianjin_r1"
  "dianjin-r1/cflue_oe.json": "process_dianjin_r1"
  "dianjin-r1/fin_qa.json": "process_dianjin_r1"

  # 目录匹配
  "fin-r1/Finance-Instruct-500k": "process_finance_instruct_500k"
  "fin-r1/FinanceIQ": "process_financeiq"
  "fin-r1/FinCUGE-Instruction": "process_fincuge_instruction"
  "fin-r1/Quant-Trading-Instruct": "process_quant_trading_instruct"
  "fin-r1/twitter-financial-news-sentiment": "process_twitter_financial_news_sentiment"
  "fin-r1/finqa": "process_finqa"
  "fin-r1/FinCorpus": "process_fincorpus"
  "fin-r1/fingpt-convfinqa": "process_convfinqa"
