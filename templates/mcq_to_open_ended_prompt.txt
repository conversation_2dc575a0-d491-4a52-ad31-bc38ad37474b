I will provide you with a multiple-choice question, and your task is to rewrite it as an open-ended question and provide a standard answer. Requirements:

1. **The question must be specific**, targeting the key points tested in the original multiple-choice question. Ensure the question is open-ended, meaning no options are provided, but it must have a clear standard answer.
2. **Based on the correct answer to the original question**, provide a concise standard answer. The answer should allow for exact matching to determine if a model's response is correct.

### Multiple-choice Question
{question}
{options}
### Correct Answer
{correct_answer}

## Strictly output in the following JSON format:

{{
    "question": "", # Open-ended Question
    "answer": ""    # Standard Answer
}}
