请根据以下标准评估推理过程的质量：
1. **内部一致性**：检查推理过程中的步骤是否一致，并在逻辑上导向正确答案。
2. **术语重叠**：检查推理过程中使用的术语与标准答案之间的重叠程度。重叠程度越高越好。
3. **推理步骤数量**：确保推理过程包含足够的步骤（至少3个步骤）。
4. **逻辑一致性**：确保推理过程中的步骤在逻辑上与标准答案一致，没有重大错误或遗漏。
5. **内容多样性**：检查推理过程是否包含重复的步骤。
6. **任务领域相关性**：确保推理与任务领域相关（任务领域：{task_domain}）。与任务领域的相关性越高，得分应越高。
7. **任务指令相关性**：检查推理是否紧密遵循任务指令。推理与任务指令的匹配程度越高，得分越高。任务指令：{task_instruction}

以下是推理过程和标准答案：
推理过程：{reasoning}
标准答案：{gold_answer}

请根据以下标准提供评分：
- 上述七个指标每项得1分。如果总分为7分，则被视为高质量的推理轨迹，输出为1。否则，输出为0。
- 请使用\\boxed{{}}格式包装分数，以便于使用正则表达式提取。例如：\\boxed{{final_score}}
