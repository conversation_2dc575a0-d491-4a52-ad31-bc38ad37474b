Please evaluate the quality of the reasoning process based on the following criteria:
1. **Internal Consistency**: Check if the steps in the reasoning process are consistent and logically lead to the correct answer.
2. **Term Overlap**: Check the overlap between terms used in the reasoning process and the standard answer. The higher the overlap, the better.
3. **Number of Reasoning Steps**: Ensure the reasoning process includes enough steps (at least 3 steps).
4. **Logical Consistency**: Ensure that the steps in the reasoning process align with the standard answer in terms of logic, with no major errors or omissions.
5. **Content Diversity**: Check if the reasoning process contains repetitive steps.
6. **Task Domain Relevance**: Ensure the reasoning is relevant to the task domain (task domain: {task_domain}). Higher relevance to the task domain should result in a higher score.
7. **Task Instruction Relevance**: Check if the reasoning closely follows the task instructions. The more closely the reasoning matches the task instructions, the higher the score. Task instructions: {task_instruction}

Below is the reasoning process and the standard answer:
Reasoning: {reasoning}
Standard Answer: {gold_answer}

Please provide a score based on the following criteria:
- The seven indicators mentioned above are scored 1 point each. If the total score is 7, it is considered a high-quality reasoning trajectory, and the output is 1. Otherwise, the output is 0.
- Please wrap the score in \\boxed{{}} format for easy extraction using regex. For example: \\boxed{{final_score}}
