#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""测试优化后的合成引擎"""

import json
import os
import tempfile
import shutil
from tools.synthesis_engine import SynthesisEngine

def create_test_data(test_dir, num_files=2, samples_per_file=5):
    """创建测试数据"""
    os.makedirs(test_dir, exist_ok=True)
    
    for file_idx in range(num_files):
        filename = f"test_file_{file_idx}.jsonl"
        filepath = os.path.join(test_dir, filename)
        
        with open(filepath, "w", encoding="utf-8") as f:
            for sample_idx in range(samples_per_file):
                data = {
                    "id": f"test_{file_idx}_{sample_idx}",
                    "content": f"这是测试数据 {file_idx}-{sample_idx}",
                    "question": f"问题 {sample_idx}？"
                }
                f.write(json.dumps(data, ensure_ascii=False) + "\n")
        
        print(f"创建测试文件: {filepath}")

def construct_prompt(data):
    """构造提示词的测试函数"""
    instruction = "请回答以下问题"
    prompt = f"问题: {data['question']}\n内容: {data['content']}"
    return instruction, prompt

def process_response(task, response, reasoning=""):
    """处理响应的测试函数"""
    return {
        "id": task["id"],
        "question": task["raw_data"]["question"],
        "answer": response,
        "reasoning": reasoning
    }

def test_synthesis_engine():
    """测试合成引擎"""
    print("开始测试合成引擎...")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        input_dir = os.path.join(temp_dir, "input")
        output_dir = os.path.join(temp_dir, "output")
        
        # 创建测试数据
        create_test_data(input_dir, num_files=2, samples_per_file=3)
        
        # 配置引擎（使用模拟配置）
        config = {
            "INPUT_DIR": input_dir,
            "OUTPUT_DIR": output_dir,
            "API_URL": "https://httpbin.org/post",  # 使用httpbin进行测试
            "API_KEY": "test-key",
            "MODEL": "test-model",
            "NUM_PROCESSORS": 2,
            "MAX_RETRIES": 2,
            "RATE_LIMIT_WAIT": 5,
            "MAX_QUEUE_SIZE": 10,
            "SYSTEM_PAUSE_TIMEOUT": 30,
            "API_PARAMS": {
                "temperature": 0.7,
                "max_tokens": 100
            }
        }
        
        # 创建引擎实例
        engine = SynthesisEngine(
            config=config,
            construct_prompt_func=construct_prompt,
            process_response_func=process_response
        )
        
        print("引擎配置完成，开始运行...")
        
        try:
            # 运行引擎
            engine.run()
            
            print("引擎运行完成")
            
            # 检查输出结果
            if os.path.exists(output_dir):
                output_files = [f for f in os.listdir(output_dir) if f.endswith('.jsonl')]
                print(f"生成的输出文件: {output_files}")
                
                for output_file in output_files:
                    output_path = os.path.join(output_dir, output_file)
                    with open(output_path, "r", encoding="utf-8") as f:
                        lines = f.readlines()
                        print(f"文件 {output_file} 包含 {len(lines)} 行结果")
            else:
                print("未找到输出目录")
                
        except Exception as e:
            print(f"测试过程中出现错误: {str(e)}")
            import traceback
            traceback.print_exc()

def test_error_handling():
    """测试错误处理"""
    print("\n开始测试错误处理...")
    
    def failing_construct_prompt(data):
        """会失败的构造提示词函数"""
        if "fail" in data.get("content", ""):
            raise Exception("构造提示词失败")
        return "指令", f"处理: {data['content']}"
    
    def failing_process_response(task, response, reasoning=""):
        """会失败的响应处理函数"""
        if "error" in response.lower():
            raise Exception("响应处理失败")
        return {"id": task["id"], "result": response}
    
    with tempfile.TemporaryDirectory() as temp_dir:
        input_dir = os.path.join(temp_dir, "input")
        output_dir = os.path.join(temp_dir, "output")
        
        # 创建包含错误数据的测试文件
        os.makedirs(input_dir, exist_ok=True)
        test_file = os.path.join(input_dir, "error_test.jsonl")
        
        with open(test_file, "w", encoding="utf-8") as f:
            # 正常数据
            f.write(json.dumps({"id": "normal_1", "content": "正常数据"}, ensure_ascii=False) + "\n")
            # 会导致构造提示词失败的数据
            f.write(json.dumps({"id": "fail_1", "content": "fail数据"}, ensure_ascii=False) + "\n")
            # 缺少id字段的数据
            f.write(json.dumps({"content": "缺少id"}, ensure_ascii=False) + "\n")
            # 无效JSON（这行会被跳过）
            f.write("invalid json line\n")
            # 正常数据
            f.write(json.dumps({"id": "normal_2", "content": "正常数据2"}, ensure_ascii=False) + "\n")
        
        config = {
            "INPUT_DIR": input_dir,
            "OUTPUT_DIR": output_dir,
            "API_URL": "https://httpbin.org/status/500",  # 模拟API错误
            "API_KEY": "test-key",
            "MODEL": "test-model",
            "NUM_PROCESSORS": 1,
            "MAX_RETRIES": 1,  # 减少重试次数以加快测试
            "RATE_LIMIT_WAIT": 2,
            "SYSTEM_PAUSE_TIMEOUT": 10
        }
        
        engine = SynthesisEngine(
            config=config,
            construct_prompt_func=failing_construct_prompt,
            process_response_func=failing_process_response
        )
        
        try:
            engine.run()
            print("错误处理测试完成")
        except Exception as e:
            print(f"错误处理测试中出现异常: {str(e)}")

if __name__ == "__main__":
    # 运行基本测试
    test_synthesis_engine()
    
    # 运行错误处理测试
    test_error_handling()
    
    print("\n所有测试完成")
